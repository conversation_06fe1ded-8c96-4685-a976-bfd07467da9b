from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from app.config.db_configuration import DatabaseConfig
from app.config.settings import settings

class DatabaseSession:
    _async_engine = None
    _async_session_factory = None

    @classmethod
    async def get_async_engine(cls):
        if cls._async_engine is None:
            db_config = DatabaseConfig(
                db_host=settings.DB_HOST,
                db_name=settings.DB_NAME,
                db_username=settings.DB_USER,
                db_password=settings.DB_PASS
            )

            # Build proper async URL for aioodbc
            base_url = db_config.sqlalchemy_url
            if "mssql+pyodbc://" in base_url:
                async_url = base_url.replace("mssql+pyodbc://", "mssql+aioodbc://")
            else:
                # Encode driver
                from urllib.parse import quote_plus
                driver = quote_plus("ODBC Driver 18 for SQL Server")

                async_url = (
                    f"mssql+aioodbc://{db_config.username}:{db_config.password}"
                    f"@{db_config.server}/{db_config.database}?driver={driver}"
                )

            cls._async_engine = create_async_engine(
                async_url,
                echo=False,
                pool_pre_ping=True,     # ✅ Valid
                pool_recycle=3600       # ✅ Valid
                # ❌ Don't use pool_size or max_overflow unless using QueuePool manually
            )

        return cls._async_engine

    @classmethod
    async def get_async_session_factory(cls):
        if cls._async_session_factory is None:
            engine = await cls.get_async_engine()
            cls._async_session_factory = async_sessionmaker(
                engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
        return cls._async_session_factory

    @classmethod
    async def get_async_session(cls):
        session_factory = await cls.get_async_session_factory()
        return session_factory()

    @classmethod
    async def close_engine(cls):
        """Close the async engine and all connections"""
        try:
            if cls._async_engine:
                await cls._async_engine.dispose()
                cls._async_engine = None
                cls._async_session_factory = None
                print("✅ MSSQL engine closed successfully")
        except Exception as e:
            print(f"❌ Error closing MSSQL engine: {e}")
            raise
