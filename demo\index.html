<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vehicle Search AI - Demo Chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            right: 60px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        .settings-btn {
            position: absolute;
            top: 15px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: background 0.3s ease;
        }

        .settings-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 10px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        /* Mandatory modal styles */
        #userInfoModal {
            z-index: 1001;
            background-color: rgba(0, 0, 0, 0.8);
        }

        #userInfoModal .modal-content {
            margin: 5% auto;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }

        /* Disable main interface when modal is open */
        .interface-disabled {
            pointer-events: none;
            opacity: 0.3;
        }

        /* Required field styling */
        .form-group label::after {
            content: "";
        }

        .form-group label[for$="Latitude"]::after,
        .form-group label[for$="Longitude"]::after,
        .form-group label[for$="Name"]::after,
        .form-group label[for$="Amount"]::after,
        .form-group label[for$="Price"]::after,
        .form-group label[for$="Zipcode"]::after,
        .form-group label[for$="SortBy"]::after,
        .form-group label[for$="OrderBy"]::after {
            content: " *";
            color: #f44336;
            font-weight: bold;
        }

        /* User info modal specific styling */
        #userInfoModal .form-group input:required,
        #userInfoModal .form-group select:required {
            border-left: 4px solid #667eea;
        }

        #userInfoModal .form-group input:required:valid,
        #userInfoModal .form-group select:required:valid {
            border-left-color: #4CAF50;
        }

        #userInfoModal .form-group input:required:invalid,
        #userInfoModal .form-group select:required:invalid {
            border-left-color: #f44336;
        }

        /* Select dropdown styling */
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            background-color: white;
            transition: border-color 0.3s ease;
        }

        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 20px;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group textarea {
            width: 100%;
            min-height: 200px;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .versions-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            margin-top: 10px;
        }

        .version-item {
            padding: 15px;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .version-item:last-child {
            border-bottom: none;
        }

        .version-item.active {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
        }

        .version-info {
            flex: 1;
        }

        .version-number {
            font-weight: 600;
            color: #667eea;
        }

        .version-date {
            font-size: 12px;
            color: #6c757d;
            margin-top: 2px;
        }

        .version-description {
            font-size: 13px;
            color: #495057;
            margin-top: 4px;
        }

        .btn-small {
            padding: 5px 10px;
            font-size: 12px;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            position: relative;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.bot .message-content {
            background: white;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 0 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }

        .message.user .message-avatar {
            background: #667eea;
            order: 2;
        }

        .message.bot .message-avatar {
            background: #4CAF50;
        }

        .filters-display {
            background: #e3f2fd;
            border: 1px solid #2196F3;
            border-radius: 10px;
            padding: 15px;
            margin-top: 10px;
        }

        .filters-title {
            font-weight: bold;
            color: #1976D2;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .filters-title::before {
            content: "🔍";
            margin-right: 8px;
        }

        .filter-item {
            background: white;
            padding: 5px 10px;
            border-radius: 15px;
            display: inline-block;
            margin: 2px;
            font-size: 12px;
            border: 1px solid #2196F3;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .chat-input-form {
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 15px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: translateY(-2px);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            padding: 15px 20px;
            background: white;
            border-radius: 20px;
            border-bottom-left-radius: 5px;
            max-width: 70%;
            border: 1px solid #e0e0e0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #667eea;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }

        .example-queries {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        .example-query {
            background: #f0f0f0;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.3s;
            border: 1px solid transparent;
        }

        .example-query:hover {
            background: #667eea;
            color: white;
        }

        .error-message {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
            padding: 10px 15px;
            border-radius: 10px;
            margin-top: 10px;
        }

        /* Vehicle info styling */
        .vehicle-info h3 {
            color: #1976D2;
            margin-bottom: 10px;
        }

        .vehicle-info p {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .vehicle-info ul {
            margin-left: 20px;
            margin-bottom: 10px;
        }

        .vehicle-info strong {
            color: #333;
        }

        /* HTML content styling for bot responses */
        .message-content h3 {
            color: #1976D2;
            margin: 15px 0 10px 0;
            font-size: 18px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 5px;
        }

        .message-content h4 {
            color: #333;
            margin: 12px 0 8px 0;
            font-size: 16px;
            font-weight: bold;
        }

        .message-content p {
            margin: 8px 0;
            line-height: 1.6;
            color: #444;
        }

        .message-content ul {
            margin: 10px 0 10px 20px;
            padding-left: 0;
        }

        .message-content li {
            margin: 5px 0;
            line-height: 1.5;
            color: #444;
        }

        .message-content strong {
            color: #1976D2;
            font-weight: 600;
        }

        .message-content em {
            font-style: italic;
            color: #666;
        }

        .message-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .message-content th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
        }

        .message-content td {
            padding: 10px 8px;
            border-bottom: 1px solid #f0f0f0;
            color: #444;
            font-size: 14px;
        }

        .message-content tr:last-child td {
            border-bottom: none;
        }

        .message-content tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .message-content a {
            color: #1976D2;
            text-decoration: none;
            border-bottom: 1px solid transparent;
            transition: border-color 0.3s;
        }

        .message-content a:hover {
            border-bottom-color: #1976D2;
        }

        /* HTML content indicator */
        .message-content.html-content {
            position: relative;
        }

        .message-content.html-content::before {
            content: "📄";
            position: absolute;
            top: -5px;
            right: -5px;
            font-size: 12px;
            opacity: 0.6;
            background: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        /* Responsive table */
        @media (max-width: 768px) {
            .message-content table {
                font-size: 12px;
            }

            .message-content th,
            .message-content td {
                padding: 8px 4px;
            }
        }

        .not-vehicle-related {
            text-align: center;
            padding: 20px;
        }

        .not-vehicle-related h3 {
            color: #ff6b35;
            margin-bottom: 15px;
        }

        .not-vehicle-related ul {
            text-align: left;
            max-width: 300px;
            margin: 15px auto;
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 95vh;
                border-radius: 10px;
            }

            .message-content {
                max-width: 85%;
            }

            .chat-header h1 {
                font-size: 20px;
            }

            .example-queries {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="status-indicator" id="statusIndicator"></div>
            <button class="settings-btn" onclick="openSettingsModal()" title="Settings">
                ⚙️
            </button>
            <h1>🚗 FastPass Vehicle Expert</h1>
            <p>Your intelligent FastPass vehicle assistant</p>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="message bot">
                <div class="message-avatar">🤖</div>
                <div class="message-content">
                    <div class="vehicle-info">
                        <h3>Welcome to FastPass! 🚗</h3>
                        <p>I'm your FastPass vehicle expert assistant. I can help you with:</p>
                        <ul>
                            <li>Vehicle specifications and features</li>
                            <li>Car comparisons and reviews</li>
                            <li>Finding vehicles available on FastPass</li>
                            <li>Current market prices and availability</li>
                            <li>Technical details and maintenance info</li>
                        </ul>
                        <p><strong>Try asking me something about vehicles!</strong></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="chat-input-container">
            <div class="example-queries">
                <div class="example-query" onclick="sendExampleQuery('show me audi a4 2023')">
                    🔍 Audi A4 2023 Filters
                </div>
                <div class="example-query" onclick="sendExampleQuery('Show me BMW X5 under 70k')">
                    🔍 BMW X5 Price Filter
                </div>
                <div class="example-query" onclick="sendExampleQuery('Tell me about 2024 Audi A4 safety features')">
                    📖 Audi A4 Safety Info
                </div>
                <div class="example-query" onclick="sendExampleQuery('Compare Honda Civic vs Toyota Corolla')">
                    ⚖️ Compare Vehicles
                </div>
            </div>
            
            <form class="chat-input-form" id="chatForm">
                <input 
                    type="text" 
                    class="chat-input" 
                    id="chatInput" 
                    placeholder="Ask me anything about vehicles..."
                    autocomplete="off"
                >
                <button type="submit" class="send-button" id="sendButton">
                    Send
                </button>
            </form>
        </div>
    </div>

    <!-- User Information Modal -->
    <div id="userInfoModal" class="modal" style="display: block;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🚗 Welcome to FastPass Vehicle Expert</h2>
                <!-- No close button - modal is mandatory -->
            </div>
            <div class="modal-body">
                <p style="margin-bottom: 20px; color: #666;">Please provide your information to get started with personalized vehicle recommendations:</p>

                <form id="userInfoForm">
                    <div class="form-group">
                        <label for="userLatitude">Latitude *</label>
                        <input type="number" id="userLatitude" step="any" placeholder="e.g., 33.994272" required>
                    </div>

                    <div class="form-group">
                        <label for="userLongitude">Longitude *</label>
                        <input type="number" id="userLongitude" step="any" placeholder="e.g., -118.39261" required>
                    </div>

                    <div class="form-group">
                        <label for="userName">Your Name *</label>
                        <input type="text" id="userName" placeholder="Enter your full name" required>
                    </div>

                    <div class="form-group">
                        <label for="userAmount">Purchase Power (Amount) *</label>
                        <input type="number" id="userAmount" step="0.01" min="0" placeholder="e.g., 50000" required>
                    </div>

                    <div class="form-group">
                        <label for="userPrice">Loan Amount Approved (Price) *</label>
                        <input type="number" id="userPrice" step="0.01" min="0" placeholder="e.g., 75000" required>
                    </div>

                    <div class="form-group">
                        <label for="userZipcode">Zipcode *</label>
                        <input type="text" id="userZipcode" placeholder="e.g., 90210" required>
                    </div>

                    <div class="form-group">
                        <label for="userSortBy">Sort By *</label>
                        <select id="userSortBy" required>
                            <option value="">Select sorting criteria</option>
                            <option value="price">Price</option>
                            <option value="year">Year</option>
                            <option value="miles">Miles</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="userOrderBy">Order By *</label>
                        <select id="userOrderBy" required>
                            <option value="">Select order</option>
                            <option value="asc">Ascending</option>
                            <option value="desc">Descending</option>
                        </select>
                    </div>

                    <div class="btn-group">
                        <button type="submit" class="btn btn-primary">Start Vehicle Search</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>⚙️ System Prompt Settings</h2>
                <button class="close-btn" onclick="closeSettingsModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="systemPrompt">System Prompt:</label>
                    <textarea id="systemPrompt" placeholder="Enter the system prompt for the AI agent..."></textarea>
                </div>
                <div class="form-group">
                    <label for="changeDescription">Description of Changes (Optional):</label>
                    <input type="text" id="changeDescription" placeholder="Describe what you changed...">
                </div>
                <div class="btn-group">
                    <button class="btn btn-secondary" onclick="loadVersions()">View Versions</button>
                    <button class="btn btn-primary" onclick="saveSystemPrompt()">Save Changes</button>
                </div>

                <div id="versionsSection" style="display: none;">
                    <h3>Version History</h3>
                    <div id="versionsList" class="versions-list">
                        <!-- Versions will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = `${window.location.protocol}//${window.location.host}/api/v1`;
        const chatMessages = document.getElementById('chatMessages');
        const chatForm = document.getElementById('chatForm');
        const chatInput = document.getElementById('chatInput');
        const sendButton = document.getElementById('sendButton');
        const statusIndicator = document.getElementById('statusIndicator');
        const auth_token = "{{ authorization_token }}";
        // Get or create user_id
        let user_id = localStorage.getItem('fastpass_user_id');
        if (!user_id) {
            user_id = `user_${Math.random().toString(36).substr(2, 9)}`;
            localStorage.setItem('fastpass_user_id', user_id);
        }

        // Initialize conversation_id (will be set by server on first message)
        let conversation_id = localStorage.getItem('fastpass_conversation_id');

        // User information variables (will be set by mandatory modal)
        let lat = null;
        let long = null;
        let userName = null;
        let userAmount = null;
        let userPrice = null;
        let userZipcode = null;
        let sort_by = null;
        let sort_order = null;

        let page = 1
        let page_size = 20

        // Flag to track if user info has been collected
        let userInfoCollected = false;
        // Check API status on load
        checkAPIStatus();

        // Initialize user interface
        initializeUserInterface();

        // Check for saved user info
        checkSavedUserInfo();

        chatForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const query = chatInput.value.trim();
            if (!query) return;

            // Check if user information has been collected
            if (!userInfoCollected) {
                alert('Please complete your information first.');
                return;
            }

            // Add user message
            addMessage(query, 'user');
            chatInput.value = '';
            
            // Show typing indicator
            showTypingIndicator();
            
            // Disable input
            setInputState(false);

            try {
                const response = await fetch(`${API_BASE_URL}/vehicle/query`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'authorization': 'Bearer '+auth_token,
                    },                    
                        body: JSON.stringify({
                        query,
                        user_id,
                        conversation_id,
                        lat,
                        long,
                        user_name: userName,
                        user_purchase_power_amount: userAmount,
                        user_loan_approval_amount: userPrice,
                        zipcode: userZipcode,
                        sort_by,
                        sort_order,
                        page,
                        page_size
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                  // Update conversation_id if returned in response
                if (data.data.metadata?.conversation_id) {
                    conversation_id = data.data.metadata.conversation_id;
                    localStorage.setItem('fastpass_conversation_id', conversation_id);
                }
                
                // Hide typing indicator
                hideTypingIndicator();
                
                // Add bot response
                addBotResponse(data.data);
                
            } catch (error) {
                hideTypingIndicator();
                addErrorMessage(`Failed to get response: ${error.message}`);
            } finally {
                setInputState(true);
                chatInput.focus();
            }
        });

        function addMessage(content, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = sender === 'user' ? '👤' : '🤖';
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.textContent = content;
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);
            
            chatMessages.appendChild(messageDiv);
            scrollToBottom();
        }

        function addBotResponse(data) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message bot';
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = '🤖';
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            
            // Check if message contains HTML content
            const messageText = data.html_content || data.message || '';

            if (messageText) {
                console.log('📄 Processing message content');

                // Check if the content is already escaped HTML
                let htmlContent = messageText;

                // If it contains escaped HTML entities, decode them
                if (htmlContent.includes('&lt;') || htmlContent.includes('&gt;')) {
                    const temp = document.createElement('div');
                    temp.innerHTML = htmlContent;
                    htmlContent = temp.textContent || temp.innerText || '';
                    console.log('🔧 Decoded escaped HTML content');
                }

                // Check if it looks like HTML content
                if (htmlContent.includes('<h') || htmlContent.includes('<p>') || htmlContent.includes('<ul>') || htmlContent.includes('<strong>')) {
                    console.log('✅ Rendering HTML content directly');
                    messageContent.innerHTML = htmlContent;
                    messageContent.classList.add('html-content');
                } else {
                    // Fallback: treat as plain text and apply basic formatting
                    console.log('📝 Treating as plain text with basic formatting');
                    messageContent.innerHTML = formatPlainTextAsHTML(htmlContent);
                }
            } else {
                console.log('⚠️ No message content found');
                messageContent.textContent = 'No response received';
            }
            
            // Add filters if available
            if (data.filters && Object.keys(data.filters).some(key => data.filters[key] !== null)) {
                const filtersDiv = document.createElement('div');
                filtersDiv.className = 'filters-display';
                
                const filtersTitle = document.createElement('div');
                filtersTitle.className = 'filters-title';
                filtersTitle.textContent = 'Extracted Search Filters:';
                filtersDiv.appendChild(filtersTitle);
                
                Object.entries(data.filters).forEach(([key, value]) => {
                    if (value !== null && value !== undefined) {
                        const filterItem = document.createElement('span');
                        filterItem.className = 'filter-item';
                        filterItem.textContent = `${key}: ${value}`;
                        filtersDiv.appendChild(filterItem);
                    }
                });
                
                messageContent.appendChild(filtersDiv);
            }
            
            // Add error styling if needed
            if (!data.success) {
                messageContent.classList.add('error-message');
            }
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);
            
            chatMessages.appendChild(messageDiv);
            scrollToBottom();
        }

        function addErrorMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message bot';
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = '⚠️';
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content error-message';
            messageContent.textContent = message;
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);
            
            chatMessages.appendChild(messageDiv);
            scrollToBottom();
        }

        function showTypingIndicator() {
            const typingDiv = document.createElement('div');
            typingDiv.className = 'message bot';
            typingDiv.id = 'typingIndicator';
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = '🤖';
            
            const typingContent = document.createElement('div');
            typingContent.className = 'typing-indicator';
            typingContent.style.display = 'block';
            
            const typingDots = document.createElement('div');
            typingDots.className = 'typing-dots';
            typingDots.innerHTML = '<div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div>';
            
            typingContent.appendChild(typingDots);
            typingDiv.appendChild(avatar);
            typingDiv.appendChild(typingContent);
            
            chatMessages.appendChild(typingDiv);
            scrollToBottom();
        }

        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        function setInputState(enabled) {
            chatInput.disabled = !enabled;
            sendButton.disabled = !enabled;
            sendButton.textContent = enabled ? 'Send' : 'Sending...';
        }

        function scrollToBottom() {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function sendExampleQuery(query) {
            chatInput.value = query;
            chatForm.dispatchEvent(new Event('submit'));
        }

        // Clear conversation history and start new conversation
        function clearConversation() {
            localStorage.removeItem('fastpass_conversation_id');
            conversation_id = null;
            chatMessages.innerHTML = `
                <div class="message bot">
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                        <div class="vehicle-info">
                            <h3>New Conversation Started</h3>
                            <p>I'm ready to help with your vehicle questions!</p>
                        </div>
                    </div>
                </div>
            `;
            // Focus input for new conversation
            chatInput.focus();
        }

        // Add clear button to header
        document.querySelector('.chat-header').insertAdjacentHTML('beforeend', 
            '<button id="clearConversation" style="position: absolute; left: 20px; background: white; border: none; border-radius: 20px; padding: 5px 10px; cursor: pointer;">🔄 New Chat</button>'
        );
        document.getElementById('clearConversation').addEventListener('click', clearConversation);

        function sanitizeHTML(html) {
            // If the HTML looks like it's showing raw tags, it might be escaped
            if (html.includes('<h3>') && html.includes('</h3>') &&
                !html.includes('&lt;') && !html.includes('&gt;')) {
                // HTML looks good, proceed with sanitization
                console.log('🔍 HTML appears to be properly formatted');
            } else if (html.includes('&lt;') || html.includes('&gt;')) {
                console.log('⚠️ HTML appears to be escaped, will decode');
            } else {
                console.log('📝 Content does not appear to be HTML');
                return html; // Return as-is if not HTML
            }

            // Basic HTML sanitization - allow safe tags only
            const allowedTags = ['h3', 'h4', 'p', 'strong', 'em', 'ul', 'li', 'table', 'tr', 'th', 'td', 'a', 'br', 'div'];
            const allowedAttributes = ['href', 'target', 'style', 'border', 'class'];

            try {
                // Create a temporary div to parse HTML
                const temp = document.createElement('div');
                temp.innerHTML = html;

                // Remove any script tags or dangerous content
                const scripts = temp.querySelectorAll('script');
                scripts.forEach(script => script.remove());

                // Remove any on* attributes (onclick, onload, etc.)
                const allElements = temp.querySelectorAll('*');
                allElements.forEach(element => {
                    // Remove dangerous attributes
                    Array.from(element.attributes).forEach(attr => {
                        if (attr.name.startsWith('on') ||
                            !allowedAttributes.includes(attr.name)) {
                            element.removeAttribute(attr.name);
                        }
                    });

                    // Remove non-allowed tags
                    if (!allowedTags.includes(element.tagName.toLowerCase())) {
                        element.outerHTML = element.innerHTML;
                    }
                });

                return temp.innerHTML;
            } catch (error) {
                console.error('❌ Error sanitizing HTML:', error);
                return html; // Return original if sanitization fails
            }
        }

        function formatResponseContent(content) {
            // Additional formatting for better display
            if (!content) return '';

            // Ensure proper spacing around headings
            content = content.replace(/<h([3-4])>/g, '<h$1 style="margin-top: 15px;">');

            // Ensure proper spacing for lists
            content = content.replace(/<ul>/g, '<ul style="margin: 10px 0;">');

            return content;
        }

        function formatPlainTextAsHTML(text) {
            // Convert plain text to basic HTML formatting
            if (!text) return '';

            // Split into paragraphs
            const paragraphs = text.split('\n\n');
            let html = '';

            for (let para of paragraphs) {
                para = para.trim();
                if (!para) continue;

                // Check if it looks like a heading (starts with ** and ends with **)
                if (para.match(/^\*\*(.*?)\*\*:?\s*$/)) {
                    const heading = para.replace(/^\*\*(.*?)\*\*:?\s*$/, '$1');
                    html += `<h4>${heading}</h4>`;
                }
                // Check for bullet points
                else if (para.includes('\n- ') || para.startsWith('- ')) {
                    const lines = para.split('\n');
                    html += '<ul>';
                    for (let line of lines) {
                        line = line.trim();
                        if (line.startsWith('- ')) {
                            const item = line.substring(2).trim();
                            // Handle bold text in list items
                            const formattedItem = item.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                            html += `<li>${formattedItem}</li>`;
                        } else if (line && !line.startsWith('- ')) {
                            // Non-bullet line in a list context
                            html += `<li>${line}</li>`;
                        }
                    }
                    html += '</ul>';
                }
                // Regular paragraph
                else {
                    // Handle bold text
                    const formattedPara = para.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                    html += `<p>${formattedPara}</p>`;
                }
            }

            return html;
        }

        async function checkAPIStatus() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`,{
                    method: 'GET',
                    headers: {
                        'authorization': 'Bearer '+ auth_token
                    }
                }
                );
                if (response.ok) {
                    statusIndicator.style.background = '#4CAF50';
                    statusIndicator.title = 'API is online';
                } else {
                    throw new Error('API not responding');
                }
            } catch (error) {
                statusIndicator.style.background = '#f44336';
                statusIndicator.title = 'API is offline';
                console.error('API health check failed:', error);
            }
        }

        // Focus input on load
        chatInput.focus();

        // Check API status every 30 seconds
        setInterval(checkAPIStatus, 30000);

        // User Information Modal Functions
        function initializeUserInterface() {
            // Disable main interface until user info is collected
            const chatContainer = document.querySelector('.chat-container');
            chatContainer.classList.add('interface-disabled');

            // Set up user info form submission
            const userInfoForm = document.getElementById('userInfoForm');
            userInfoForm.addEventListener('submit', handleUserInfoSubmission);

            // Pre-fill with default values if available
            document.getElementById('userLatitude').value = 33.994272;
            document.getElementById('userLongitude').value = -118.39261;
            document.getElementById('userSortBy').value = 'price';
            document.getElementById('userOrderBy').value = 'asc';
        }

        function handleUserInfoSubmission(e) {
            e.preventDefault();

            // Get form values
            const latitude = parseFloat(document.getElementById('userLatitude').value);
            const longitude = parseFloat(document.getElementById('userLongitude').value);
            const name = document.getElementById('userName').value.trim();
            const amount = parseFloat(document.getElementById('userAmount').value);
            const price = parseFloat(document.getElementById('userPrice').value);
            const zipcode = document.getElementById('userZipcode').value.trim();
            const sortBy = document.getElementById('userSortBy').value;
            const orderBy = document.getElementById('userOrderBy').value;

            // Validate all fields are filled
            if (!latitude || !longitude || !name || !amount || !price || !zipcode || !sortBy || !orderBy) {
                alert('Please fill in all required fields.');
                return;
            }

            // Validate numeric values
            if (isNaN(latitude) || isNaN(longitude) || isNaN(amount) || isNaN(price)) {
                alert('Please enter valid numeric values for latitude, longitude, amount, and price.');
                return;
            }

            if (amount <= 0 || price <= 0) {
                alert('Amount and price must be greater than 0.');
                return;
            }

            // Store user information
            lat = latitude;
            long = longitude;
            userName = name;
            userAmount = amount;
            userPrice = price;
            userZipcode = zipcode;
            sort_by = sortBy;
            sort_order = orderBy;
            userInfoCollected = true;

            // Save to localStorage for future sessions
            localStorage.setItem('fastpass_user_info', JSON.stringify({
                lat, long, userName, userAmount, userPrice, userZipcode, sort_by, sort_order
            }));

            // Hide modal and enable interface
            document.getElementById('userInfoModal').style.display = 'none';
            const chatContainer = document.querySelector('.chat-container');
            chatContainer.classList.remove('interface-disabled');

            // Focus on chat input
            chatInput.focus();

            // Show welcome message with user info
            addWelcomeMessage();
        }

        function addWelcomeMessage() {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message bot';

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = '🤖';

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.innerHTML = `
                <div class="vehicle-info">
                    <h3>Welcome ${userName}! 🚗</h3>
                    <p>Your profile has been set up successfully:</p>
                    <ul>
                        <li><strong>Location:</strong> ${lat}, ${long}</li>
                        <li><strong>Zipcode:</strong> ${userZipcode}</li>
                        <li><strong>Purchase Power:</strong> $${userAmount.toLocaleString()}</li>
                        <li><strong>Loan Amount Approved:</strong> $${userPrice.toLocaleString()}</li>
                        <li><strong>Sort By:</strong> ${sort_by} (${sort_order})</li>
                    </ul>
                    <p>I'm ready to help you find the perfect vehicle within your budget and preferences!</p>
                </div>
            `;

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);

            // Replace the default welcome message
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = '';
            chatMessages.appendChild(messageDiv);
            scrollToBottom();
        }

        // Check if user info was previously saved
        function checkSavedUserInfo() {
            const savedInfo = localStorage.getItem('fastpass_user_info');
            if (savedInfo) {
                try {
                    const userInfo = JSON.parse(savedInfo);
                    if (userInfo.lat && userInfo.long && userInfo.userName && userInfo.userAmount &&
                        userInfo.userPrice && userInfo.userZipcode && userInfo.sort_by && userInfo.sort_order) {
                        // Pre-fill form with saved data
                        document.getElementById('userLatitude').value = userInfo.lat;
                        document.getElementById('userLongitude').value = userInfo.long;
                        document.getElementById('userName').value = userInfo.userName;
                        document.getElementById('userAmount').value = userInfo.userAmount;
                        document.getElementById('userPrice').value = userInfo.userPrice;
                        document.getElementById('userZipcode').value = userInfo.userZipcode;
                        document.getElementById('userSortBy').value = userInfo.sort_by;
                        document.getElementById('userOrderBy').value = userInfo.sort_order;
                    }
                } catch (error) {
                    console.error('Error loading saved user info:', error);
                }
            }
        }

        // System Prompt Management Functions
        function openSettingsModal() {
            document.getElementById('settingsModal').style.display = 'block';
            loadCurrentPrompt();
        }

        function closeSettingsModal() {
            document.getElementById('settingsModal').style.display = 'none';
            document.getElementById('versionsSection').style.display = 'none';
        }

        // Close modal when clicking outside (but not the mandatory user info modal)
        window.onclick = function(event) {
            const settingsModal = document.getElementById('settingsModal');
            const userInfoModal = document.getElementById('userInfoModal');

            if (event.target === settingsModal) {
                closeSettingsModal();
            }
            // Don't allow closing the user info modal by clicking outside
            // if user info hasn't been collected yet
        }

        async function loadCurrentPrompt() {
            try {
                const response = await fetch('/api/v1/system-prompt/current', {
                    method: 'GET',
                    headers: {
                        'authorization': 'Bearer ' + auth_token
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    console.log('API Response:', result); // Debug log

                    // The response structure is: { status_code, data: { prompt, success }, message }
                    if (result.data && result.data.prompt) {
                        document.getElementById('systemPrompt').value = result.data.prompt;
                        console.log('System prompt loaded successfully');
                    } else {
                        console.error('No prompt found in response:', result);
                    }
                } else {
                    console.error('Failed to load current prompt, status:', response.status);
                }
            } catch (error) {
                console.error('Error loading current prompt:', error);
            }
        }

        async function saveSystemPrompt() {
            const prompt = document.getElementById('systemPrompt').value.trim();
            const description = document.getElementById('changeDescription').value.trim();

            if (!prompt) {
                alert('Please enter a system prompt');
                return;
            }

            try {
                const response = await fetch('/api/v1/system-prompt/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'authorization': 'Bearer ' + auth_token
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        description: description || 'Updated via demo interface',
                        created_by: 'demo_user'
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    console.log('Save response:', result); // Debug log

                    // Check if the operation was successful
                    if (result.data && result.data.success) {
                        alert('System prompt updated successfully!');
                        document.getElementById('changeDescription').value = '';
                        // Reload versions if they're visible
                        if (document.getElementById('versionsSection').style.display !== 'none') {
                            loadVersions();
                        }
                    } else {
                        alert('Failed to update system prompt: ' + (result.data?.message || result.message || 'Unknown error'));
                    }
                } else {
                    const errorResult = await response.json();
                    alert('Failed to update system prompt: ' + (errorResult.message || 'Server error'));
                }
            } catch (error) {
                console.error('Error saving system prompt:', error);
                alert('Error saving system prompt');
            }
        }

        async function loadVersions() {
            try {
                const response = await fetch('/api/v1/system-prompt/versions', {
                    method: 'GET',
                    headers: {
                        'authorization': 'Bearer ' + auth_token
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    console.log('Versions response:', result); // Debug log

                    // Check the response structure: { status_code, data: { versions, success }, message }
                    if (result.data && result.data.versions) {
                        displayVersions(result.data.versions);
                        document.getElementById('versionsSection').style.display = 'block';
                    } else {
                        console.error('No versions found in response:', result);
                    }
                } else {
                    console.error('Failed to load versions, status:', response.status);
                }
            } catch (error) {
                console.error('Error loading versions:', error);
            }
        }

        function displayVersions(versions) {
            const versionsList = document.getElementById('versionsList');
            versionsList.innerHTML = '';

            versions.forEach(version => {
                const versionItem = document.createElement('div');
                versionItem.className = 'version-item' + (version.is_active ? ' active' : '');

                versionItem.innerHTML = `
                    <div class="version-info">
                        <div class="version-number">Version ${version.version} ${version.is_active ? '(Active)' : ''}</div>
                        <div class="version-date">${new Date(version.created_at).toLocaleString()}</div>
                        <div class="version-description">${version.description || 'No description'}</div>
                        <div style="font-size: 12px; color: #6c757d;">By: ${version.created_by}</div>
                    </div>
                    <div>
                        ${!version.is_active ? `<button class="btn btn-small btn-primary" onclick="restoreVersion(${version.version})">Restore</button>` : ''}
                        <button class="btn btn-small btn-secondary" onclick="previewVersion(${version.version})">Preview</button>
                    </div>
                `;

                versionsList.appendChild(versionItem);
            });
        }

        async function restoreVersion(versionNumber) {
            if (!confirm(`Are you sure you want to restore version ${versionNumber}?`)) {
                return;
            }

            try {
                const response = await fetch('/api/v1/system-prompt/restore', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'authorization': 'Bearer ' + auth_token
                    },
                    body: JSON.stringify({
                        version: versionNumber,
                        created_by: 'demo_user'
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    console.log('Restore response:', result); // Debug log

                    // Check the response structure
                    if (result.data && result.data.success) {
                        alert(`Version ${versionNumber} restored successfully!`);
                        loadCurrentPrompt();
                        loadVersions();
                    } else {
                        alert('Failed to restore version: ' + (result.data?.message || result.message || 'Unknown error'));
                    }
                } else {
                    const errorResult = await response.json();
                    alert('Failed to restore version: ' + (errorResult.message || 'Server error'));
                }
            } catch (error) {
                console.error('Error restoring version:', error);
                alert('Error restoring version');
            }
        }

        function previewVersion(versionNumber) {
            // Find the version in the current versions list
            const versionsList = document.getElementById('versionsList');
            const versionItems = versionsList.querySelectorAll('.version-item');

            versionItems.forEach(item => {
                const versionText = item.querySelector('.version-number').textContent;
                if (versionText.includes(`Version ${versionNumber}`)) {
                    const description = item.querySelector('.version-description').textContent;
                    const date = item.querySelector('.version-date').textContent;
                    const createdBy = item.querySelector('div[style*="font-size: 12px"]').textContent;

                    alert(`Version ${versionNumber} Details:\n\nDate: ${date}\n${createdBy}\nDescription: ${description}`);
                }
            });
        }
    </script>
</body>
</html>
