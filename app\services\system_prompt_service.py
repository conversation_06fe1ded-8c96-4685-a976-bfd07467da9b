from datetime import datetime, timezone
from typing import List, Optional
from motor.motor_asyncio import AsyncIOMotorClient
from fastapi import Request
from app.config.settings import settings
from app.models.company_settings import CompanySettings, SystemPromptVersion, PyObjectId
from app.config.logger import log_info, log_error

class SystemPromptService:
    _client_pool = None
    _db_pool = None

    def __init__(self, req: Request):
        self.client = None
        self.db = None
        self.company_settings = None
        self.req = req
        self.company_id = "fastpass_default"  # Default company ID
        self.setting_type = "system_prompt"
        self.setting_name = "vehicle_expert_prompt"

    @classmethod
    async def get_shared_client(cls):
        """Get or create shared MongoDB client with connection pooling"""
        if cls._client_pool is None:
            cls._client_pool = AsyncIOMotorClient(
                settings.MONGODB_URI,
                maxPoolSize=200,
                minPoolSize=20,
                maxIdleTimeMS=30000,
                waitQueueTimeoutMS=5000,
                serverSelectionTimeoutMS=5000
            )

        return cls._client_pool

    @classmethod
    async def get_shared_database(cls):
        """Get or create shared MongoDB database connection"""
        if cls._db_pool is None:
            client = await cls.get_shared_client()
            cls._db_pool = client.get_database(settings.MONGODB_DATABASE)

        return cls._db_pool

    @classmethod
    async def close_shared_pools(cls):
        """Close shared connection pools - for application shutdown"""
        try:
            if cls._client_pool:
                cls._client_pool.close()
                cls._client_pool = None
                cls._db_pool = None
                print("SystemPromptService: MongoDB connection pools closed.")
        except Exception as e:
            print(f"Error closing SystemPromptService MongoDB pools: {e}")

    async def connect(self):
        """Initialize MongoDB connection using shared connection pool"""
        try:
            # Use shared connection pool
            self.db = await self.get_shared_database()
            self.client = await self.get_shared_client()
            self.company_settings = self.db.company_settings

            log_info(self.req, "SystemPromptService MongoDB connection established using shared pool.")
        except Exception as e:
            log_error(self.req, f"Failed to connect to MongoDB: {e}")
            raise

    async def close(self):
        """Close MongoDB connection - using shared pool, so just clear references"""
        try:
            # With shared connection pool, we just clear local references
            # The actual connections remain in the pool for reuse
            self.client = None
            self.db = None
            self.company_settings = None

            log_info(self.req, "SystemPromptService MongoDB connection cleanup completed (shared pool).")
        except Exception as e:
            log_error(self.req, f"Error while cleaning up MongoDB connection: {e}")

    async def get_current_prompt(self) -> Optional[str]:
        """Get the current active system prompt"""
        try:
            settings_doc = await self.company_settings.find_one({
                "company_id": self.company_id,
                "setting_type": self.setting_type,
                "setting_name": self.setting_name
            })
            
            if not settings_doc:
                # Return default prompt if none exists
                return self._get_default_prompt()
            
            company_settings = CompanySettings(**settings_doc)
            current_version = company_settings.current_version
            
            # Find the current active version
            for version in company_settings.versions:
                if version.version == current_version and version.is_active:
                    log_info(self.req, f"Retrieved system prompt version {current_version}")
                    return version.prompt
            
            # Fallback to default if no active version found
            log_info(self.req, "No active version found, returning default prompt")
            return self._get_default_prompt()
            
        except Exception as e:
            log_error(self.req, f"Error retrieving current prompt: {e}")
            return self._get_default_prompt()

    async def update_prompt(self, prompt: str, description: str = None, created_by: str = "system") -> dict:
        """Update system prompt with versioning"""
        try:
            settings_doc = await self.company_settings.find_one({
                "company_id": self.company_id,
                "setting_type": self.setting_type,
                "setting_name": self.setting_name
            })
            
            if settings_doc:
                company_settings = CompanySettings(**settings_doc)
                # Deactivate current version
                for version in company_settings.versions:
                    version.is_active = False
                
                # Create new version
                new_version_number = max([v.version for v in company_settings.versions], default=0) + 1
            else:
                # Create new settings document
                company_settings = CompanySettings(
                    company_id=self.company_id,
                    setting_type=self.setting_type,
                    setting_name=self.setting_name,
                    current_version=1,
                    versions=[]
                )
                new_version_number = 1
            
            # Add new version
            new_version = SystemPromptVersion(
                version=new_version_number,
                prompt=prompt,
                created_by=created_by,
                is_active=True,
                description=description
            )
            
            company_settings.versions.append(new_version)
            company_settings.current_version = new_version_number
            company_settings.updated_at = datetime.now(timezone.utc)
            
            # Save to database
            await self.company_settings.replace_one(
                {
                    "company_id": self.company_id,
                    "setting_type": self.setting_type,
                    "setting_name": self.setting_name
                },
                company_settings.model_dump(by_alias=True),
                upsert=True
            )
            
            log_info(self.req, f"System prompt updated to version {new_version_number}")
            return {
                "success": True,
                "message": f"System prompt updated to version {new_version_number}",
                "version": new_version_number
            }
            
        except Exception as e:
            log_error(self.req, f"Error updating system prompt: {e}")
            return {
                "success": False,
                "message": f"Failed to update system prompt: {str(e)}"
            }

    async def get_all_versions(self) -> List[SystemPromptVersion]:
        """Get all versions of the system prompt"""
        try:
            settings_doc = await self.company_settings.find_one({
                "company_id": self.company_id,
                "setting_type": self.setting_type,
                "setting_name": self.setting_name
            })
            
            if not settings_doc:
                return []
            
            company_settings = CompanySettings(**settings_doc)
            log_info(self.req, f"Retrieved {len(company_settings.versions)} prompt versions")
            return sorted(company_settings.versions, key=lambda x: x.version, reverse=True)
            
        except Exception as e:
            log_error(self.req, f"Error retrieving prompt versions: {e}")
            return []

    async def restore_version(self, version_number: int, created_by: str = "system") -> dict:
        """Restore a specific version of the system prompt"""
        try:
            settings_doc = await self.company_settings.find_one({
                "company_id": self.company_id,
                "setting_type": self.setting_type,
                "setting_name": self.setting_name
            })
            
            if not settings_doc:
                return {
                    "success": False,
                    "message": "No system prompt settings found"
                }
            
            company_settings = CompanySettings(**settings_doc)
            
            # Find the version to restore
            version_to_restore = None
            for version in company_settings.versions:
                if version.version == version_number:
                    version_to_restore = version
                    break
            
            if not version_to_restore:
                return {
                    "success": False,
                    "message": f"Version {version_number} not found"
                }
            
            # Create a new version based on the restored one
            return await self.update_prompt(
                prompt=version_to_restore.prompt,
                description=f"Restored from version {version_number}",
                created_by=created_by
            )
            
        except Exception as e:
            log_error(self.req, f"Error restoring prompt version: {e}")
            return {
                "success": False,
                "message": f"Failed to restore version: {str(e)}"
            }

    def _get_default_prompt(self) -> str:
        """Get the default system prompt"""
        return """
You are FastPass, a professional AI vehicle buying expert designed to guide users through the car purchasing process with personalized, expert-driven recommendations using a provided vehicle database and general vehicle information from the `websearch_tool`. Your goal is to understand the user's intent, recommend vehicles with their name (make, model, year, trim), features (e.g., safety, fuel efficiency, technology), approximate cost, and relevant details (e.g., reliability, depreciation, suitability for location), and deliver actionable next steps. When the user expresses interest in a vehicle (e.g., "I like this vehicle" or "I'm interested"), generate a JSON object with filters (make, model, year, trim) for database querying. Always maintain a natural, professional tone, and ensure the user feels understood and confident in their decision-making.

Use the `websearch_tool` to fetch general vehicle information (e.g., features, approximate cost, reliability, safety ratings) to enhance recommendations, but never include dealer names, website names, or specific inventory details in responses. Recommendations should be based on general vehicle knowledge, assumed database context, and web-searched data, excluding any dealer or website references.

Follow this step-by-step process for every interaction:
1. Understand the user's intent and requirements
2. Use available tools to gather relevant information
3. Provide personalized recommendations based on user context
4. Offer actionable next steps
"""
