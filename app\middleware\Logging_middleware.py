from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from fastapi import Request, Response
from app.config.rest_api_logger import log_info
import json
import time

class LoggingMiddleware(BaseHTTPMiddleware):
    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next):
        request.state.start_time = time.time()

        # Capture request body if method allows
        try:
            if request.method in ["POST", "PUT", "PATCH"]:
                body_bytes = await request.body()

                try:
                    request.state.payload = json.loads(body_bytes.decode("utf-8"))
                except Exception:
                    request.state.payload = {"raw": body_bytes.decode("utf-8", errors="ignore")}

                # Rebuild the request stream so downstream handlers can read it
                request._receive = _create_receive_override(body_bytes)

            elif request.method == "GET":
                request.state.payload = dict(request.query_params)
            else:
                request.state.payload = {}

        except Exception as e:
            print(f"[LoggingMiddleware] Error reading request body: {e}")
            request.state.payload = {}

        # Process the actual request
        try:
            response = await call_next(request)

            # Read and store the response body
            response_body = b""
            async for chunk in response.body_iterator:
                response_body += chunk

            # Try to decode the JSON response
            try:
                response_text = response_body.decode("utf-8")
                response_data = json.loads(response_text)
            except Exception:
                response_data = {"raw": response_body.decode("utf-8", errors="ignore")}

            request.state.response_data = response_data
            request.state.status_code = response.status_code

            # Log everything
            await log_info(request)

            # Return a fresh Response to client with original data
            final_response = Response(
                content=response_body,
                status_code=response.status_code,
                headers=dict(response.headers),
                media_type=response.media_type
            )

            return final_response

        except Exception as e:
            raise

# Rebuild request body stream for downstream handlers
def _create_receive_override(body_bytes: bytes):
    async def receive():
        return {"type": "http.request", "body": body_bytes, "more_body": False}
    return receive
