from fastapi import <PERSON><PERSON>out<PERSON>, HTTPException, Depends, Request, Body
from fastapi.responses import JSONResponse
from app.models.company_settings import SystemPromptRequest, SystemPromptResponse, RestoreVersionRequest
from app.services.system_prompt_service import SystemPromptService
from app.config.logger import log_info, log_error
from app.utils.constant import constant
from app.utils.helper import helper
from app.utils.message import message

router = APIRouter()


@router.get("/system-prompt/current")
async def get_current_system_prompt(request: Request):
    """
    Get the current active system prompt
    """
    try:
        log_info(request, "Fetching current system prompt...")

        prompt_service = SystemPromptService(request)
        await prompt_service.connect()

        current_prompt = await prompt_service.get_current_prompt()

        await prompt_service.close()

        response_data = {
            "prompt": current_prompt,
            "success": True
        }

        log_info(request, "Current system prompt fetched successfully")
        return helper.return_response(constant.SUCCESS, response_data, "Current system prompt retrieved successfully")

    except Exception as e:
        log_error(request, f"Error fetching current system prompt: {str(e)}")
        return helper.return_error(constant.FAIL, "Failed to fetch current system prompt")


@router.post("/system-prompt/update")
async def update_system_prompt(request: Request, body: SystemPromptRequest = Body(...)):
    """
    Update the system prompt with versioning
    """
    try:
        log_info(request, "Updating system prompt...")

        prompt_service = SystemPromptService(request)
        await prompt_service.connect()

        result = await prompt_service.update_prompt(
            prompt=body.prompt,
            description=body.description,
            created_by=body.created_by
        )

        await prompt_service.close()

        if result["success"]:
            log_info(request, f"System prompt updated successfully to version {result.get('version')}")
            return helper.return_response(constant.SUCCESS, result, "System prompt updated successfully")
        else:
            log_error(request, f"Failed to update system prompt: {result['message']}")
            return helper.return_error(constant.FAIL, result["message"])

    except Exception as e:
        log_error(request, f"Error updating system prompt: {str(e)}")
        return helper.return_error(constant.FAIL, "Failed to update system prompt")


@router.get("/system-prompt/versions")
async def get_system_prompt_versions(request: Request):
    """
    Get all versions of the system prompt
    """
    try:
        log_info(request, "Fetching system prompt versions...")

        prompt_service = SystemPromptService(request)
        await prompt_service.connect()

        versions = await prompt_service.get_all_versions()

        await prompt_service.close()

        response_data = {
            "versions": [version.model_dump() for version in versions],
            "success": True
        }

        log_info(request, f"Retrieved {len(versions)} system prompt versions")
        return helper.return_response(constant.SUCCESS, response_data, "System prompt versions retrieved successfully")

    except Exception as e:
        log_error(request, f"Error fetching system prompt versions: {str(e)}")
        return helper.return_error(constant.FAIL, "Failed to fetch system prompt versions")


@router.post("/system-prompt/restore")
async def restore_system_prompt_version(request: Request, body: RestoreVersionRequest = Body(...)):
    """
    Restore a specific version of the system prompt
    """
    try:
        log_info(request, f"Restoring system prompt to version {body.version}...")

        prompt_service = SystemPromptService(request)
        await prompt_service.connect()

        result = await prompt_service.restore_version(
            version_number=body.version,
            created_by=body.created_by
        )

        await prompt_service.close()

        if result["success"]:
            log_info(request, f"System prompt restored to version {body.version}")
            return helper.return_response(constant.SUCCESS, result, "System prompt version restored successfully")
        else:
            log_error(request, f"Failed to restore system prompt: {result['message']}")
            return helper.return_error(constant.FAIL, result["message"])

    except Exception as e:
        log_error(request, f"Error restoring system prompt version: {str(e)}")
        return helper.return_error(constant.FAIL, "Failed to restore system prompt version")
