from fastapi.responses import JSONResponse

class Helper:
    def __init__(self):
        pass

    def return_response(self, status_code: int, data, message: str):
        return {
            "status_code": status_code,
            "data": data,
            "message": message
        }

    def return_error(self, status_code: int, message: str):  # <- Add self here
        return JSONResponse(status_code=status_code, content={"message": message})

helper = Helper()
