"""
Simple FastMCP HTTP Server for Vehicle Search AI
Exposes query_vehicle_expert tool via HTTP transport
"""

import asyncio
from fastmcp import FastMCP
from pydantic import BaseModel, Field
from fastapi import Request, Body
from app.api.routes import query_vehicle_expert
from app.models.requests import VehicleQueryRequest

# Initialize FastMCP server with HTTP transport
mcp = FastMCP("Vehicle Search AI")

class VehicleQueryParams(BaseModel):
    """Parameters for vehicle query tool"""
    query: str = Field(..., description="User's vehicle-related question")
    user_id: int = Field(..., description="ID of the user")
    conversation_id: str = Field(None, description="ID of the conversation")
    lat: float = Field(..., description="Latitude for the user location")
    long: float = Field(..., description="Longitude for the user location")
    page: int = Field(1, description="Current page of the inventory")
    page_size: int = Field(20, description="Total vehicles per page")

class MockRequest:
    """Mock request object for the existing route"""
    def __init__(self, user_id: int):
        self.state = type('obj', (object,), {
            'user': {'user_id': user_id},
            'user_details': {
                'member_first_name': 'MCP',
                'member_last_name': 'User',
                'user_total_purchase_power': 100000
            }
        })()

@mcp.tool()
async def vehicle_query(params: VehicleQueryParams):
    """
    Query the fastpass vehicle expert agent with natural language.
    Uses the existing query_vehicle_expert route function.
    """
    try:
        print(f"🔍 Received vehicle query: {params.query}")

        # Create mock request and body
        mock_request = MockRequest(params.user_id)
        body = VehicleQueryRequest(
            query=params.query,
            user_id=params.user_id,
            conversation_id=params.conversation_id,
            lat=params.lat,
            long=params.long,
            page=params.page,
            page_size=params.page_size
        )

        # Call the existing route function
        result = await query_vehicle_expert(mock_request, body)
        print("✅ Query processed successfully")
        return result

    except Exception as e:
        print(f"❌ Error processing query: {e}")
        return {"error": str(e), "success": False}

if __name__ == "__main__":
    # Run the MCP server with HTTP transport
    from app.config.settings import settings
    import os

    host = os.getenv('MCP_SERVER_HOST', settings.mcp_server_host)
    port = int(os.getenv('MCP_SERVER_PORT', settings.mcp_server_port))

    print(f"🔧 Starting FastMCP server on {host}:{port}/mcp")
    print(f"🛠️  Available tools: {len(mcp.tools)} tools registered")

    mcp.run(transport="http", host=host, port=port, path="/mcp")
