# AZURE KEY VAULT CREDENTIALS
AZURE_KEY_VAULT_NAME=XXXXXXXXXXXXXXXXXXX
AZURE_TENANT_ID=XXXXXXXXXXXXXXXX
AZURE_CLIENT_ID=XXXXXXXXXXXXXX
AZURE_CLIENT_SECRET="XXXXXXXXXXXXXXXXXXX"
# DEALER FILE
DEALER_FILE_PATH= "####################"
# TOKEN - THIS TOKEN IS REQIRED ONLY WHEN 
# SEARCH IS BEING MADE FROM THE FRONTEND
# RENDERED THORUGHT FAST API
AUTHORIZATION_TOKEN="XXXXXXXXXXXXXXXXXXXXXX"
# Demo url is the frontend rendered through server for testing
# make it flase while deploying in production
ALLOW_DEMO_URL=False
# Local logging configuration for development
LOCAL_LOGGING_ENABLED=true