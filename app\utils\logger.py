"""Centralized logging configuration for the LLM Orchestrator.

This module:
Creates a logs directory if it doesn't exist
Configures logging to both file and console
Uses daily log file rotation
Provides a pre-configured logger instance
"""

import logging
import os
from datetime import datetime
from logging.handlers import TimedRotatingFileHandler
# Create logs directory if it doesn't exist
LOG_DIR = "logs"
os.makedirs(LOG_DIR, exist_ok=True)

# Log file named with today's date
TODAY = datetime.now().strftime("%Y-%m-%d")
LOG_FILE = os.path.join(LOG_DIR, f"app-{TODAY}.log")

# Configure logging with:
# - INFO level as default
# - Timestamped format
# - Output to both file and console
file_handler = TimedRotatingFileHandler(
    LOG_FILE,
    when="midnight",
    interval=1,
    backupCount=15,
    encoding="utf-8",
    utc=False
)
file_handler.suffix = "%Y-%m-%d"
file_handler.setFormatter(logging.Formatter("[%(asctime)s] %(levelname)s - %(message)s"))

console_handler = logging.StreamHandler()
console_handler.setFormatter(logging.Formatter("[%(asctime)s] %(levelname)s - %(message)s"))

# Pre-configured logger instance for the application
logger = logging.getLogger("LLM-Orchestrator")
logger.setLevel(logging.INFO)
logger.handlers = []  # Clear existing handlers
logger.addHandler(file_handler)
logger.addHandler(console_handler)