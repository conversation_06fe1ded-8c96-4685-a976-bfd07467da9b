from fastapi import APIRouter, HTTPException, Depends, Request, Body
from fastapi.responses import JSONResponse
from app.models.requests import TestInventoryRequest
from app.services.mc_vehicle_inventory_service import VehicleInventoryService
from app.services.proximity_service import ProximityService
from app.config.logger import log_info, log_error
from app.utils.constant import constant
from app.utils.helper import helper
from app.utils.message import message

router = APIRouter()


@router.post("/test/inventory")
async def test_inventory_filtering(request: Request, body: TestInventoryRequest = Body(...)):
    """
    Test endpoint for inventory filtering functionality
    """
    try:
        log_info(request, f"Testing inventory filtering with filters: {body.filters}")

        # Get user details from request state (from JWT middleware)
        user_details = request.state.user_details
        approved_amount = user_details.get('user_total_purchase_power', 50000)

        # Get nearby dealers
        proximity_service = ProximityService(request)
        person_location = {"lat": body.lat, "lon": body.long}

        # Override max distance if provided
        if body.max_distance:
            proximity_service.max_user_distance = body.max_distance

        dealers = await proximity_service.read_dealers_from_file(proximity_service.dealers_path)

        nearby_dealers = await proximity_service.find_nearby_dealers(
            person_location, dealers, proximity_service.max_user_distance
        )

        dealer_ids = [int(dealer["dealer_id"]) for dealer in nearby_dealers]

        log_info(request, f"Found {len(nearby_dealers)} nearby dealers within {proximity_service.max_user_distance} miles")

        # Get inventory using the filter data
        inventory_service = VehicleInventoryService(request)
        await inventory_service.connect()
        try:
            inventory_vehicles = await inventory_service.get_filtered_inventory(
                filters=body.filters,
                approved_amount=approved_amount,
                req=request,
                page=body.page,
                page_size=body.page_size,
                sort_by=body.sort_by,
                sort_order=body.sort_order,
                dealer_ids=dealer_ids
            )
        finally:
            await inventory_service.close()

        # Prepare response
        response_data = {
            "success": True,
            "vehicles_found": len(inventory_vehicles),
            "vehicles": inventory_vehicles,
        }

        log_info(request, f"Successfully fetched {len(inventory_vehicles)} vehicles from inventory")
        return helper.return_response(constant.SUCCESS, response_data, "Inventory filtering test completed successfully")

    except Exception as e:
        log_error(request, f"Error testing inventory filtering: {str(e)}")
        return helper.return_error(constant.FAIL, f"Inventory filtering test failed: {str(e)}")
