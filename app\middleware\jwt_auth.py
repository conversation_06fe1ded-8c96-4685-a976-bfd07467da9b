from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
from jose import jwt, JWTError, ExpiredSignatureError
import textwrap
from app.config.rest_api_logger import log_info
from app.config.logger import log_error
from app.utils.helper import helper
from app.utils.constant import constant
from app.utils.message import message
import os
import json
import uuid
class JWTAuthMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, public_key: str):
        super().__init__(app)
        self.pem_key = self.pemFormatPublicKey(public_key)
        self.public_paths = ["/"]
    async def dispatch(self, request: Request, call_next):
        request.state.user = {"member_id":""}
        request.state.transaction = str(uuid.uuid4())

        if request.method in ['POST','GET']:
            await log_info(request)
            #setting the transaction id

        # Skip JWT validation for preflight requests
        if request.method == "OPTIONS":
            return await call_next(request)

        if request.url.path in self.public_paths:
            return await call_next(request)

        auth_header = request.headers.get("authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            log_error(request, f"Missing or invalid Authorization Header: {auth_header}")
            return helper.return_error(constant.UNAUTHORIZATION, message.bearer_token_mising_or_invalid)

        token = auth_header.split(" ")[1]

        try:
            payload = jwt.decode(token, self.pem_key, algorithms=["RS256"], options={"verify_sub": False} )

            user_details = {"member_id":payload["sub"]}

            if not user_details:
                log_error(request, f"User details not found against the payload: {json.dumps(payload)} having authorization token: {token}")
                return helper.return_error(constant.UNAUTHORIZATION,message.unauthorized_request)

            request.state.user = {"member_id":payload["sub"]}
            request.state.user_details = user_details

        except ExpiredSignatureError as e:
            log_error(request, f"Token has expired: {str(e)}")
            return helper.return_error(constant.UNAUTHORIZATION, message.token_expired)

        except JWTError as e:
            log_error(request, f"Invalid token: {str(e)}")
            return helper.return_error(constant.UNAUTHORIZATION, message.invalid_token)

        response = await call_next(request)

        return response

    @staticmethod
    def pemFormatPublicKey(public_key: str):
        base64_key_clean = public_key.replace(" ", "").replace("\n", "")
        wrapped_key = "\n".join(textwrap.wrap(base64_key_clean, 64))
        pem_key = f"-----BEGIN PUBLIC KEY-----\n{wrapped_key}\n-----END PUBLIC KEY-----"
        return pem_key
