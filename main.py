from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from app.api.routes import router
from app.config.settings import settings
from app.services.fetch_key_value_azure_service import AzureEnvLoader
from app.middleware.jwt_auth import JWTAuthMiddleware
from app.config.db_configuration import DatabaseConfig
from app.middleware.Logging_middleware import LoggingMiddleware
import uvicorn
import os
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
templates = Jinja2Templates(directory="demo")
from dotenv import load_dotenv
from app.utils.constant import constant
from app.utils.helper import helper
from app.utils.message import message
load_dotenv()
# Create FastAPI application
app = FastAPI(
    title="Vehicle Search AI Microservice",
    description="AI-powered vehicle search and information service using LangGraph, OpenAI, and SerpAPI",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.add_middleware(JWTAuthMiddleware, public_key=settings.JWT_PUBLIC_KEY)
app.add_middleware(LoggingMiddleware)
# Include API routes
app.include_router(router, prefix="/api/v1")

# Serve demo frontend
if os.path.exists("demo"):
    app.mount("/demo", StaticFiles(directory="demo"), name="demo")

@app.get("/", response_class=HTMLResponse)
async def serve_demo(request: Request):
    """Serve the demo frontend at root with env values injected"""
    template_path = "index.html"
    full_path = os.path.join("demo", template_path)
    print(settings.ALLOW_DEMO_URL)
    if os.path.exists(full_path) and settings.ALLOW_DEMO_URL:
        return templates.TemplateResponse(template_path, {
            "request": request,
            "authorization_token": os.getenv('AUTHORIZATION_TOKEN')
        })
    else:
        return helper.return_error(constant.FAIL, message.demo_interface_not_allowed)

@app.on_event("startup")
async def startup_event():
    """Startup event handler"""
    print("🚗 Vehicle Search AI Microservice starting up...")
    print(f"Environment: {settings.ENVIRONMENT}")
    print(f"OpenAI API Key configured: {'Yes' if settings.OPENAI_API_KEY else 'No'}")
    print(f"SerpAPI Key configured: {'Yes' if settings.SERPAPI_API_KEY else 'No'}")
    print(f"Azure vault name found: {'Yes' if settings.azure_key_vault_name else 'No'}")
    print(f"Azure tenant id configured: {'Yes' if settings.azure_tenant_id else 'No'}"   )
    print(f"Azure client id configured: {'Yes' if settings.azure_client_id else 'No'}")
    print(f"Azure client secret configured: {'Yes' if settings.azure_client_secret else 'No'}")

@app.on_event("shutdown")
async def shutdown_event():
    """Shutdown event handler"""
    try:
        print("🚗 Vehicle Search AI Microservice shutting down...")

        # Close MongoDB connection pools properly
        try:
            from app.services.system_prompt_service import SystemPromptService
            from app.services.memory_service import MemoryService

            await SystemPromptService.close_shared_pools()
            await MemoryService.close_shared_pools()
            print("✅ MongoDB connection pools closed successfully")
        except Exception as e:
            print(f"❌ Error closing MongoDB pools: {e}")

        # Close MSSQL engine if needed
        try:
            from app.db.session import DatabaseSession
            if hasattr(DatabaseSession, 'close_engine'):
                await DatabaseSession.close_engine()
            print("✅ MSSQL engine closed successfully")
        except Exception as e:
            print(f"❌ Error closing MSSQL engine: {e}")

        print("🔄 Shutdown complete")
    except Exception as e:
        print(f"❌ Critical error during shutdown: {e}")
        # Don't re-raise to avoid blocking shutdown

if __name__ == "__main__":

    db_config = DatabaseConfig(
        db_host=settings.DB_HOST,
        db_name=settings.DB_NAME,
        db_username=settings.DB_USER,
        db_password=settings.DB_PASS
    )
    SQLALCHEMY_DATABASE_URL = db_config.sqlalchemy_url
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True if settings.ENVIRONMENT == "development" else False
    )
