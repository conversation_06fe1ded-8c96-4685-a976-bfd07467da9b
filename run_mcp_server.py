#!/usr/bin/env python3
"""
Simple MCP HTTP Server Runner for Vehicle Search AI
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.mcp.server import mcp
from app.config.settings import settings
from dotenv import load_dotenv
import os

def main():
    """Main entry point for MCP server"""
    load_dotenv()

    # Get host and port from environment variables
    host = os.getenv('MCP_SERVER_HOST', settings.mcp_server_host)
    port = int(os.getenv('MCP_SERVER_PORT', settings.mcp_server_port))

    print(f"🚗 Starting Vehicle Search AI MCP HTTP Server on {host}:{port}...")

    try:
        if __name__ == "__main__":
            mcp.run(transport="http", host=host, port=port, path="/mcp")
    except KeyboardInterrupt:
        print("\n🛑 MCP Server stopped")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
