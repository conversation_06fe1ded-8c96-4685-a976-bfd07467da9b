# Vehicle Search AI Microservice

An intelligent vehicle search and information microservice built with Python, FastAPI, LangGraph, OpenAI, and SerpAPI.

## Features

- 🤖 **AI Vehicle Expert**: Acts as a knowledgeable vehicle expert using OpenAI GPT-4
- 🔍 **Real-time Web Search**: Uses SerpAPI for current vehicle information, prices, and reviews
- 🎯 **Smart Filter Extraction**: Automatically extracts search filters from natural language queries
- 📱 **HTML Responses**: Returns formatted HTML content for easy frontend integration
- 🛡️ **Query Validation**: Ensures only vehicle-related questions are processed
- 🔧 **Multiple Tools**: LangGraph-based agent with specialized vehicle tools

## API Capabilities

### 1. Vehicle Information Queries
Ask any vehicle-related question and get comprehensive, real-time information.

**Example:**
```
Query: "Tell me about the 2023 Toyota Camry"
Response: HTML-formatted information with specs, reviews, and current market data
```

### 2. Vehicle Search with Filters
Request specific vehicles and get JSON filters for database queries.

**Example:**
```
Query: "Show me Audi A4 2022 models under $50,000"
Response: JSON filters + HTML explanation
{
  "make": "Audi",
  "model": "A4",
  "year_min": 2022,
  "year_max": 2022,
  "price_max": 50000
}
```

### 3. Vehicle Comparisons
Compare multiple vehicles side by side.

**Example:**
```
Query: "Compare Honda Civic vs Toyota Corolla 2023"
Response: Detailed comparison in HTML format
```

## Setup Instructions

### 1. Create Virtual Environment
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Environment Configuration
Create a `.env` file in the root directory:
```env
OPENAI_API_KEY=your_openai_api_key_here
SERPAPI_API_KEY=your_serpapi_key_here
ENVIRONMENT=development
LOG_LEVEL=INFO
```

### 4. Run the Service
```bash
# Using the development runner
python run.py

# Or directly with uvicorn
uvicorn main:app --reload --host 127.0.0.1 --port 8000
```

## API Endpoints

### POST `/api/v1/vehicle/query`
Main endpoint for vehicle queries.

**Request:**
```json
{
  "query": "Show me BMW X5 2023 models under $70,000"
}
```

**Response:**
```json
{
  "success": true,
  "response_type": "search_filters",
  "message": "Here are BMW X5 2023 models under $70,000...",
  "html_content": "<div class='vehicle-info'>...</div>",
  "filters": {
    "make": "BMW",
    "model": "X5",
    "year_min": 2023,
    "year_max": 2023,
    "price_max": 70000
  },
  "metadata": {
    "sources": ["web_search"]
  }
}
```

### GET `/api/v1/health`
Health check endpoint.

### GET `/api/v1/`
Service information and available endpoints.

## Project Structure

```
vehicle-search-ai/
├── app/
│   ├── agents/
│   │   └── vehicle_agent.py      # LangGraph vehicle expert agent
│   ├── api/
│   │   └── routes.py             # FastAPI routes
│   ├── config/
│   │   └── settings.py           # Configuration settings
│   ├── models/
│   │   ├── requests.py           # Request models
│   │   └── responses.py          # Response models
│   ├── tools/
│   │   ├── web_search_tool.py    # SerpAPI integration
│   │   ├── vehicle_filter_tool.py # Filter extraction
│   │   └── vehicle_info_tool.py  # Vehicle information tools
│   └── utils/
│       ├── formatters.py         # HTML formatting utilities
│       └── validators.py         # Query validation
├── main.py                       # FastAPI application
├── run.py                        # Development server runner
├── requirements.txt              # Python dependencies
├── .env.example                  # Environment variables template
└── README.md                     # This file
```

## Usage Examples

### 1. Information Query
```bash
curl -X POST "http://localhost:8000/api/v1/vehicle/query" \
  -H "Content-Type: application/json" \
  -d '{"query": "What are the safety features of 2023 Honda Accord?"}'
```

### 2. Search Query with Filters
```bash
curl -X POST "http://localhost:8000/api/v1/vehicle/query" \
  -H "Content-Type: application/json" \
  -d '{"query": "Find me electric SUVs under $60,000"}'
```

### 3. Non-Vehicle Query (Error Response)
```bash
curl -X POST "http://localhost:8000/api/v1/vehicle/query" \
  -H "Content-Type: application/json" \
  -d '{"query": "What is the weather today?"}'
```

## Integration with Your Database

When the API returns `response_type: "search_filters"`, use the `filters` object to query your vehicle database:

```python
# Example integration
response = requests.post("http://localhost:8000/api/v1/vehicle/query", 
                        json={"query": "Show me Audi A4 2022 under $50k"})

if response.json()["response_type"] == "search_filters":
    filters = response.json()["filters"]
    
    # Use filters in your database query
    vehicles = db.query(Vehicle).filter(
        Vehicle.make == filters["make"],
        Vehicle.model == filters["model"],
        Vehicle.year >= filters["year_min"],
        Vehicle.price <= filters["price_max"]
    ).all()
```

## API Documentation

Once the service is running, visit:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## Development

### Adding New Tools
1. Create a new tool in `app/tools/`
2. Inherit from `BaseTool`
3. Add to the agent's tools list in `vehicle_agent.py`

### Customizing Responses
Modify the HTML formatting in `app/utils/formatters.py` to match your frontend styling.

### Environment Variables
- `OPENAI_API_KEY`: Your OpenAI API key
- `SERPAPI_API_KEY`: Your SerpAPI key
- `ENVIRONMENT`: development/production
- `LOG_LEVEL`: INFO/DEBUG/WARNING/ERROR

## Demo Frontend

A beautiful chat interface is included for testing the API with a real-time chat experience.

### Quick Start (Windows)
```bash
# Double-click or run:
start_demo.bat
```

### Quick Start (All Platforms)
```bash
# 1. Create and activate virtual environment
python -m venv venv
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac

# 2. Install dependencies
pip install -r requirements.txt

# 3. Configure API keys
cp .env.example .env
# Edit .env and add your OpenAI and SerpAPI keys

# 4. Start demo
python start_demo.py
```

### ⚠️ Important Setup Notes:
- **Virtual Environment**: Always activate your virtual environment first
- **API Keys Required**: You need OpenAI API key for AI filter extraction
- **SerpAPI Optional**: SerpAPI key is optional but recommended for web search
- **Python 3.8+**: Make sure you're using Python 3.8 or higher

### Manual Setup
1. Start the API server: `python run.py`
2. Open http://localhost:8000 in your browser
3. Start chatting with the vehicle expert!

### Demo Features
- 💬 **Real-time Chat Interface** with typing indicators
- 🎨 **Beautiful UI** with gradient design and animations
- 📱 **Responsive Design** works on mobile and desktop
- 🔍 **Filter Display** shows extracted search parameters
- ⚡ **Quick Examples** for easy testing
- 🟢 **API Status Indicator** shows connection status
- 🚗 **Vehicle-focused** responses with proper formatting

### Testing the Demo
Try these example queries in the chat interface:

**AI Filter Extraction (No Web Search):**
- "show me audi a4 2023"
- "Show me BMW X5 under 70k"
- "find toyota camry 2022"
- "get me electric SUVs under $60,000"

**Information Queries (Web Search + AI Response):**
- "Tell me about Toyota Camry safety features"
- "What are the specs of BMW X5?"
- "Compare Honda Civic vs Toyota Corolla"
- "toyota camry review"

**Non-Vehicle Queries (Should be rejected):**
- "What's the weather today?"
- "Tell me a joke"

### API Testing
```bash
# Test the API directly
python test_api.py

# Test AI filter extraction specifically
python test_ai_filters.py

# Manual API test
curl -X POST "http://localhost:8000/api/v1/vehicle/query" \
  -H "Content-Type: application/json" \
  -d '{"query": "Show me Audi A4 under 50k"}'
```

## License

This project is licensed under the MIT License.
