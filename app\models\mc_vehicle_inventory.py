from sqlalchemy import (
    Column, Integer, String, Text, DECIMAL, DateTime, Boolean, create_engine
)
from sqlalchemy.dialects.mssql import DATETIMEOFFSET, NVARCHAR
from app.db.base import Base

class McVehicleInventory(Base):
    __tablename__ = 'mc_vehicle_inventory'

    row_id = Column(Integer, primary_key=True, nullable=False)
    id = Column(String(64), nullable=False)
    vin = Column(String(17), nullable=False)
    heading = Column(String(256), nullable=True, default=None)
    more_info = Column(Text, nullable=True, default=None)
    price = Column(Integer, nullable=True, default=None)
    msrp = Column(String(200), nullable=True, default=None)
    miles = Column(Integer, nullable=True, default=None)
    stock_no = Column(String(32), nullable=True, default=None)
    year = Column(Integer, nullable=True, default=None)
    make = Column(String(32), nullable=True, default=None)
    model = Column(String(256), nullable=True, default=None)
    trim = Column(String(256), nullable=True, default=None)
    vehicle_type = Column(String(64), nullable=True, default=None)
    body_type = Column(String(64), nullable=True, default=None)
    body_subtype = Column(String(64), nullable=True, default=None)
    drivetrain = Column(String(64), nullable=True, default=None)
    fuel_type = Column(String(64), nullable=True, default=None)
    engine = Column(String(64), nullable=True, default=None)
    engine_block = Column(String(8), nullable=True, default=None)
    engine_size = Column(String(16), nullable=True, default=None)
    transmission = Column(String(32), nullable=True, default=None)
    doors = Column(Integer, nullable=True, default=None)
    cylinders = Column(String(5), nullable=True, default=None)
    city_mpg = Column(String(10), nullable=True, default=None)
    highway_mpg = Column(String(10), nullable=True, default=None)
    interior_color = Column(String(64), nullable=True, default=None)
    exterior_color = Column(String(64), nullable=True, default=None)
    base_exterior_color = Column(String(64), nullable=True, default=None)
    base_interior_color = Column(String(64), nullable=True, default=None)
    is_certified = Column(Integer, nullable=True, default=None)
    is_transfer = Column(Integer, nullable=True, default=None)
    taxonomy_vin = Column(String(10), nullable=True, default=None)
    model_code = Column(String(24), nullable=True, default=None)
    scraped_at = Column(String(24), nullable=True, default=None)
    status_date = Column(String(24), nullable=True, default=None)
    first_scraped_at = Column(String(24), nullable=True, default=None)
    source = Column(String(256), nullable=True, default=None)
    seller_name = Column(String(512), nullable=True, default=None)
    street = Column(String(512), nullable=True, default=None)
    city = Column(String(128), nullable=True, default=None)
    state = Column(String(128), nullable=True, default=None)
    zip = Column(String(10), nullable=True, default=None)
    latitude = Column(DECIMAL(11, 6), nullable=True, default=None)
    longitude = Column(DECIMAL(11, 6), nullable=True, default=None)
    coordinates = Column(Text, nullable=True, default=None)
    country = Column(String(2), nullable=True, default=None)
    seller_phone = Column(String(256), nullable=True, default=None)
    seller_email = Column(String(256), nullable=True, default=None)
    seller_type = Column(String(32), nullable=True, default=None)
    listing_type = Column(String(32), nullable=True, default=None)
    inventory_type = Column(String(16), nullable=True, default=None)
    dealer_type = Column(String(32), nullable=True, default=None)
    car_seller_name = Column(String(512), nullable=True, default=None)
    car_address = Column(String(512), nullable=True, default=None)
    car_street = Column(String(512), nullable=True, default=None)
    car_city = Column(String(128), nullable=True, default=None)
    car_state = Column(String(128), nullable=True, default=None)
    car_zip = Column(String(128), nullable=True, default=None)
    car_latitude = Column(DECIMAL(11, 6), nullable=True, default=None)
    car_longitude = Column(DECIMAL(11, 6), nullable=True, default=None)
    seller_comments = Column(Text, nullable=True, default=None)
    options = Column(Text, nullable=True, default=None)
    features = Column(Text, nullable=True, default=None)
    photo_links = Column(Text, nullable=True, default=None)
    photo_url = Column(Text, nullable=True, default=None)
    dom = Column(Integer, nullable=True, default=None)
    dom_180 = Column(Integer, nullable=True, default=None)
    dom_active = Column(Integer, nullable=True, default=None)
    currency_indicator = Column(String(32), nullable=True, default=None)
    miles_indicator = Column(String(32), nullable=True, default=None)
    carfax_1_owner = Column(Integer, nullable=True, default=None)
    carfax_clean_title = Column(Integer, nullable=True, default=None)
    loan_term = Column(String(10), nullable=True, default=None)
    loan_apr = Column(DECIMAL(18, 6), nullable=True, default=None)
    l_down_pay = Column(Integer, nullable=True, default=None)
    l_emi = Column(Integer, nullable=True, default=None)
    f_down_pay = Column(DECIMAL(18, 2), nullable=True, default=None)
    f_down_pay_per = Column(Integer, nullable=True, default=None)
    f_emi = Column(Integer, nullable=True, default=None)
    lease_term = Column(String(10), nullable=True, default=None)
    in_transit = Column(Integer, nullable=True, default=None)
    in_transit_days = Column(Integer, nullable=True, default=None)
    in_transit_at = Column(String(24), nullable=True, default=None)
    dol = Column(Integer, nullable=True, default=None)
    category = Column(String(32), nullable=True, default=None)
    dealership_group_id = Column(Text, nullable=True, default=None)
    sub_dealership_group_id = Column(Integer, nullable=True, default=None)
    dealership_group_name = Column(String(256), nullable=True, default=None)
    sub_dealership_group_name = Column(String(256), nullable=True, default=None)
    dealer_id = Column(Integer, nullable=True, default=None)
    location_id = Column(Integer, nullable=True, default=None)
    website_id = Column(Integer, nullable=True, default=None)
    is_searchable = Column(Integer, nullable=True, default=None)
    fp_dealer_id = Column(Integer, nullable=True, default=None)
    neo_features = Column(Text, nullable=True, default=None)
    high_value_features = Column(Text, nullable=True, default=None)
    rooftop_id = Column(Integer, nullable=True, default=None)
    derived_inventory_type = Column(String(100), nullable=True, default=None)
    updated_at = Column(DateTime, nullable=True, default=None)
    sold_out = Column(Boolean, nullable=False, default=False)
