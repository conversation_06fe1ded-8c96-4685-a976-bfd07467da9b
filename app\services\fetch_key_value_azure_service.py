from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient
import os

class AzureEnvLoader:
    def __init__(self, vault_name: str):
        self.vault_url = f"https://{vault_name}.vault.azure.net/"
        self.credential = DefaultAzureCredential()
        self.client = SecretClient(vault_url=self.vault_url, credential=self.credential)

    def normalize_key(self, key: str) -> str:
        """Convert AZURE_KEY style to Python-style snake_case (e.g., DB-HOST → db_host)"""
        return key.replace("-", "_")

   # In app/services/fetch_key_value_azure_service.py
    def load_all_secrets_to_environment(self) -> dict:
        loaded_secrets = {}

        try:
            properties = self.client.list_properties_of_secrets()
            for prop in properties:
                original_name = prop.name
                normalized_name = self.normalize_key(original_name)

                if normalized_name in os.environ:
                    print(f"⚠️  Skipping {normalized_name}, already in environment")
                    continue

                try:
                    secret = self.client.get_secret(original_name)
                    os.environ[normalized_name] = secret.value
                    # loaded_secrets[normalized_name] = secret.value
                    print(f"✅ Loaded {normalized_name}")
                except Exception as e:
                    print(f"❌ Failed to load secret {original_name}: {e}")
        except Exception as e:
            print(f"❌ Failed to list secrets: {e}")
        
        # return loaded_secrets

