# Simple MCP HTTP Server - Quick Start

## 🚀 Start in 2 Minutes

### Step 1: Install FastMCP
```bash
pip install fastmcp
```

### Step 2: Configure Environment
```bash
# Set your API keys in .env
OPENAI_API_KEY=your_key
SERPAPI_API_KEY=your_key
MONGODB_URI=mongodb://localhost:27017

# Optional: Customize MCP server host/port
MCP_SERVER_HOST=localhost
MCP_SERVER_PORT=3001
```

### Step 3: Start MCP HTTP Server
```bash
python run_mcp_server.py
```
Server runs on `http://{MCP_SERVER_HOST}:{MCP_SERVER_PORT}/mcp`

### Step 4: Configure Claude Desktop
Add to `claude_desktop_config.json`:
```json
{
  "mcpServers": {
    "vehicle-search-ai": {
      "url": "http://localhost:3001/mcp"
    }
  }
}
```

## 🎯 Available Tool

**vehicle_query**: Ask any vehicle question
- "Show me BMW X5 under $50k"
- "Compare Honda vs Toyota"
- "What are Camry safety features?"

## ✅ That's it!

The MCP server exposes the existing `query_vehicle_expert` function via HTTP transport.

## 📋 Files Checklist

### ✅ Required Files Created:
- `app/mcp/__init__.py` - Python package init
- `app/mcp/server.py` - Simple FastMCP HTTP server
- `run_mcp_server.py` - Server runner script
- `mcp_config.json` - Claude Desktop config
- `test_mcp.py` - Test script to verify setup

### ✅ Updated Files:
- `requirements.txt` - Added `fastmcp` dependency
- `.env.example` - Added MCP server settings

### 🔧 Test Setup:
```bash
# Test if everything is ready
python test_mcp.py

# If tests pass, start server
python run_mcp_server.py
```

### 📝 Environment Variables Needed:
```bash
# Copy .env.example to .env and set:
OPENAI_API_KEY=your_key
SERPAPI_API_KEY=your_key
MONGODB_URI=mongodb://localhost:27017
MONGODB_DBNAME=fastpass_ai

# MCP Server settings (with defaults)
MCP_SERVER_ENABLED=true
MCP_SERVER_HOST=localhost  # Default: localhost
MCP_SERVER_PORT=3001       # Default: 3001
```
