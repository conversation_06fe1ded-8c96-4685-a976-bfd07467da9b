from fastapi import APIRouter, HTTPException, Depends, Request, Body
from fastapi.responses import JSONResponse
from app.config.logger import log_info, log_error
from app.utils.constant import constant
from app.utils.helper import helper
from app.utils.message import message

router = APIRouter()


@router.get("/health")
async def health_check(request: Request):
    """Health check endpoint"""
    try:
        data = {
            "status": "healthy",
            "service": "vehicle-search-ai"}
        return helper.return_response(constant.SUCCESS,data, message.health_status_success)
    except Exception as e:
        log_error(request, f"Health check failed: {str(e)}")
        return helper.return_error(constant.FAIL, message.health_status_error)

@router.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "service": "Vehicle Search AI Microservice",
        "version": "1.0.0",
        "description": "AI-powered vehicle search and information service",
        "endpoints": {
            "POST /vehicle/query": "Query the vehicle expert agent",
            "GET /health": "Health check",
            "GET /debug/html": "Debug HTML formatting",
            "GET /docs": "API documentation"
        },
        "features": [
            "Vehicle information lookup",
            "Real-time web search integration",
            "Vehicle search filter extraction",
            "HTML-formatted responses",
            "Vehicle expert knowledge"
        ]
    }
