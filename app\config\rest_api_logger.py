import logging
import uuid
import os
import socket
import json
import time
import subprocess
import async<PERSON>
from concurrent.futures import Thread<PERSON>oolExecutor
from datetime import datetime
from app.config.settings import settings
from fastapi import Request
from azure.storage.blob import BlobServiceClient
from azure.core.exceptions import ResourceExistsError
from starlette.responses import Response
from logging.handlers import TimedRotatingFileHandler

# Load environment variables
AZURE_BLOB_ACCOUNT = settings.AZURE_BLOB_ACCOUNT
AZURE_BLOB_KEY = settings.AZURE_BLOB_KEY
LOG_ENVIRONMENT = settings.LOG_ENVIRONMENT
CONTAINER_NAME = settings.BLOB_CONTAINER_NAME
EC2 = os.getenv('EC2', socket.gethostname())

# Azure Blob Setup - Only if credentials are available
blob_service_client = None
container_client = None

if AZURE_BLOB_ACCOUNT and AZURE_BLOB_KEY:
    try:
        blob_service_client = BlobServiceClient(
            account_url=f"https://{AZURE_BLOB_ACCOUNT}.blob.core.windows.net/",
            credential=AZURE_BLOB_KEY
        )
        container_client = blob_service_client.get_container_client(CONTAINER_NAME)

        try:
            container_client.create_container()
        except ResourceExistsError:
            pass
        print("✅ Azure Blob Storage REST API logging enabled")
    except Exception as e:
        print(f"⚠️ Azure Blob Storage REST API setup failed: {e}")
        blob_service_client = None
        container_client = None
else:
    print("⚠️ Azure Blob Storage credentials not found - REST API logging to Azure disabled")

def format_ip(ip):
    if not ip:
        return "Unknown IP"
    if ip.startswith("::ffff:"):
        return ip.split(":")[-1]
    return ip

def get_current_commit_hash():
    try:
        commit_hash = subprocess.check_output(
            ['git', 'log', '--format=%H', '-n', '1', 'HEAD']
        ).decode('utf-8').strip()
        return commit_hash[:7]
    except Exception as e:
        print(f"Error retrieving commit hash: {e}")
        return "unknown"

def get_rest_api_blob_name():
    date_str = datetime.utcnow().strftime('%Y-%m-%d')
    return f"{LOG_ENVIRONMENT}-fastpass-ai-microservice-rest-api-requests-response-{date_str}.log"

# Custom JSON Formatter
class CustomJsonFormatter(logging.Formatter):
    def format(self, record):
        metadata = getattr(record, "metadata", {})
        log_entry = {
            "@timestamp": datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S.%fZ'),
            "V": metadata.get("V", ""),
            "EC2": metadata.get("EC2", ""),
            "IP": metadata.get("IP", ""),
            "PU": metadata.get("PU", ""),
            "TID": metadata.get("TID", ""),
            "api_endpoint": metadata.get("api_endpoint", ""),
            "MEMBER_ID": metadata.get("MEMBER_ID", ""),
            "LEVEL": metadata.get("LEVEL", "").lower(),
            "PATH": metadata.get("PATH", ""),
            "METHOD": metadata.get("METHOD", ""),
            "PARAM": metadata.get("PARAM", {}),
            "STATUS": metadata.get("STATUS", ""),
            "RESPONSE": metadata.get("RESPONSE", {}),
            "ET": metadata.get("ET", ""),
        }
        return json.dumps(log_entry)

# Logger and handler
logger = logging.getLogger("azureRestApiLogger")
logger.setLevel(logging.DEBUG)
logger.propagate = False
logging.getLogger("azure.core.pipeline.policies.http_logging_policy").setLevel(logging.WARNING)

class AzureBlobRestApiHandler(logging.Handler):
    def __init__(self):
        super().__init__()
        self.executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="azure_blob_rest_api_logger")

    def emit(self, record):
        # Only log to Azure if container_client is available
        if container_client is None:
            return

        # Submit to thread pool for async execution - don't wait for completion
        try:
            msg = self.format(record)
            self.executor.submit(self._async_emit_to_blob, msg)

        except Exception as e:
            print(f"Failed to submit REST API log to async thread: {e}")

    def _async_emit_to_blob(self, msg):
        """Execute the actual blob write in a separate thread"""
        try:
            blob_name = get_rest_api_blob_name()
            blob_client = container_client.get_blob_client(blob_name)

            # ✅ Ensure it's created as an Append Blob
            if not blob_client.exists():
                blob_client.create_append_blob()

            # ✅ Append log entry
            blob_client.append_block(msg + "\n")

        except Exception as e:
            print(f"Failed to write to Azure Blob (async): {e}")

class LocalFileRestApiHandler(logging.Handler):
    """Local file handler for REST API logs that mirrors Azure Blob functionality with async pattern"""
    def __init__(self):
        super().__init__()
        self.executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="local_file_rest_api_logger")
        # Create logs directory if it doesn't exist
        os.makedirs("logs", exist_ok=True)

    def emit(self, record):
        # Submit to thread pool for async execution - don't wait for completion
        try:
            msg = self.format(record)
            self.executor.submit(self._async_emit_to_file, msg)
        except Exception as e:
            print(f"Failed to submit REST API log to local file async thread: {e}")

    def _async_emit_to_file(self, msg):
        """Execute the actual file write in a separate thread"""
        try:
            log_filename = self._get_local_rest_api_log_filename()
            log_path = os.path.join("logs", log_filename)

            with open(log_path, "a", encoding="utf-8") as f:
                f.write(msg + "\n")
        except Exception as e:
            print(f"Failed to write to local REST API log file (async): {e}")

    def _get_local_rest_api_log_filename(self):
        """Generate local REST API log filename similar to Azure blob naming"""
        date_str = datetime.utcnow().strftime('%Y-%m-%d')
        return f"fastpass-ai-microservice-rest-api-requests-response-{date_str}.log"



# Add local file handler if LOCAL_LOGGING_ENABLED is true
if settings.LOCAL_LOGGING_ENABLED:
    local_rest_api_handler = LocalFileRestApiHandler()
    local_rest_api_handler.setFormatter(CustomJsonFormatter())
    logger.addHandler(local_rest_api_handler)
    print("✅ Local file logging enabled for REST API logger")
elif not settings.LOCAL_LOGGING_ENABLED:
    azure_handler = AzureBlobRestApiHandler()
    azure_handler.setFormatter(CustomJsonFormatter())
    logger.addHandler(azure_handler)

# Logging Helper
async def log_info(req: Request):
    try:
        if req.method == 'POST':
            payload = await req.json()
        elif req.method == 'GET':
            payload = dict(req.query_params)
        else:
            payload = {}

        headers = req.headers
        path = req.url.path
        method = req.method
        client_ip = headers.get("x-forwarded-for") or req.client.host
        transaction_id = req.state.transaction
        member_id = getattr(req.state.user, "member_id", "")

        response_data = getattr(req.state, "response_data", {})
        status_code = getattr(req.state, "status_code", 200)
        start_time = getattr(req.state, "start_time", time.time())
        elapsed_time = round(time.time() - start_time, 2)

        metadata = {
            "V": get_current_commit_hash(),
            "EC2": EC2,
            "IP": format_ip(client_ip),
            "PU": req.url.hostname or "",
            "TID": transaction_id,
            "api_endpoint": path,
            "MEMBER_ID": member_id,
            "LEVEL": "INFO",
            "PATH": str(req.url),
            "METHOD": method,
            "PARAM": payload,
            "STATUS": status_code,
            "RESPONSE": response_data,
            "ET": str(elapsed_time)
        }

        logger.info("REST API call", extra={"metadata": metadata})

    except Exception as e:
        print(f"Logger info error: {e}")

