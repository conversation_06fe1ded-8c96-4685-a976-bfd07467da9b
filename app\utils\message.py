class Message:
    def __init__(self, **entries):
        self.__dict__.update(entries)

message_dict = {
    "fetched_vehicle_agent_success": "Agent response fetched Successfully",
    "fetched_vehicle_agent_error":   "Failed to fetch agent response",
    "internal_server_error": "Internal Server Error",
    "health_status_success": "Health status for AI serivce fetched Successfully",
    "health_status_error": "Failed to fetch AI serivce status",
    "fetch_chat_history_list_success": 'AI microservices chat history list fetched successfully',
    "fetch_chat_history_list_error": "Failed to fetch chat history list",
    "agent_response_error":"Failed to fetch vehicle agent response",
    "bearer_token_mising_or_invalid": 'Missing or invalid Authorization header',
    "unauthorized_request": "Unauthorized Request",
    "token_expired": "Token Expired",
    "invalid_token":"Invalid Token",
    "fetch_user_conversation_success":"Fetch user conversation successfully",
    'fetch_user_conversation_error':"Failed to fetch user conversation",
    "feedback_provided_successfully":"User feedback added successfully",
    "feedback_provided_error":"Failed to add user feedback",
    "coversation_deleted_successfully":"Conversation deleted successfully",
    "coversation_deleted_error":"Failed to delete conversation",
    "demo_interface_not_allowed": "You do not have permission to access this resource",
    "message_updated_failed":"Message not found or could not be updated",
    "message_update_success": "Message updated Successfully",
    "message_update_error": "Failed to update message"
}

message = Message(**message_dict)