#!/usr/bin/env python3
"""
Simple HTTP server to serve the demo frontend
Run this alongside your main API server
"""
import http.server
import socketserver
import webbrowser
import os
import sys

def serve_demo(port=3000):
    """Serve the demo frontend on specified port"""
    
    # Change to demo directory
    demo_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(demo_dir)
    
    # Create server
    handler = http.server.SimpleHTTPRequestHandler
    
    try:
        with socketserver.TCPServer(("", port), handler) as httpd:
            print(f"🌐 Demo frontend server starting...")
            print(f"📱 Demo URL: http://localhost:{port}")
            print(f"🚗 Make sure your API is running on http://localhost:8000")
            print(f"📖 API Docs: http://localhost:8000/docs")
            print(f"⏹️  Press Ctrl+C to stop the server")
            print("-" * 50)
            
            # Open browser automatically
            webbrowser.open(f"http://localhost:{port}")
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Demo server stopped")
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ Port {port} is already in use. Try a different port:")
            print(f"   python demo/serve_demo.py --port 3001")
        else:
            print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    port = 3000
    
    # Check for port argument
    if len(sys.argv) > 1:
        if "--port" in sys.argv:
            try:
                port_index = sys.argv.index("--port") + 1
                port = int(sys.argv[port_index])
            except (IndexError, ValueError):
                print("❌ Invalid port number. Using default port 3000")
                port = 3000
    
    serve_demo(port)
