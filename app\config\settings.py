from pydantic_settings import BaseSettings
import os
from dotenv import load_dotenv
from app.services.fetch_key_value_azure_service import AzureEnvLoader
print("🔄 Loading environment variables from .env...")
vault_secrets = {}
load_dotenv()
# to prevent multiple calls from the vault if already fetched 
if os.environ.get("JWT_PUBLIC_KEY",'') == '':
    loader = AzureEnvLoader(vault_name=os.getenv("AZURE_KEY_VAULT_NAME"))
    loader.load_all_secrets_to_environment()
# Loop through all environment variables and copy them to vault_secrets
for key, value in os.environ.items():
    vault_secrets[key] = value

class Settings(BaseSettings):
    """Application settings"""

    # Azure details
    azure_key_vault_name: str = os.getenv('AZURE_KEY_VAULT_NAME',"")
    azure_tenant_id: str = os.getenv("AZURE_TENANT_ID","")
    azure_client_id: str = os.getenv("AZURE_CLIENT_ID","")
    azure_client_secret: str = os.getenv("AZURE_CLIENT_SECRET","")
    
    # Dealer File Path
    DEALER_FILE_PATH: str = os.getenv('DEALER_FILE_PATH',"dealer_master_dev")
    ALLOW_DEMO_URL: bool = os.getenv('ALLOW_DEMO_URL',False)

    # Local logging configuration
    LOCAL_LOGGING_ENABLED: bool = os.getenv('LOCAL_LOGGING_ENABLED', 'false').lower() == 'true'
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = 'allow'


env_settings = Settings()
combined = {**vault_secrets, **env_settings.dict()} 
# ✅ Feed secrets directly into Settings constructor
settings = Settings(**combined)

