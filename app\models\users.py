# app/models/user.py

from sqlalchemy import (
    Column, Integer, String, Numeric, Boolean, Text
)
from sqlalchemy.dialects.mssql import DATETIMEOFFSET, NVARCHAR
from app.db.base import Base

class User(Base):
    __tablename__ = "users"
    __table_args__ = {"schema": "dbo"}

    user_id                     = Column(Integer,   primary_key=True)
    member_id                   = Column(Integer,   nullable=False)
    member_first_name           = Column(NVARCHAR(255), nullable=True)
    member_last_name            = Column(NVARCHAR(255), nullable=True)
    member_email                = Column(NVARCHAR(255), nullable=True)
    preapproved_pct             = Column(Numeric(10,2), nullable=True)
    preapproved_max_amt         = Column(Numeric(10,2), nullable=True)
    preapproved_expire          = Column(DATETIMEOFFSET, nullable=True)
    lender_nm                   = Column(NVARCHAR(100), nullable=True)
    branch_address_line_1       = Column(NVARCHAR(255), nullable=True)
    branch_address_city_nm      = Column(NVARCHAR(255), nullable=True)
    branch_address_state_cd     = Column(NVARCHAR(20),  nullable=True)
    branch_local_phone          = Column(NVARCHAR(20),  nullable=True)
    lender_agent_name           = Column(NVARCHAR(255), nullable=True)
    lender_agent_primary_phn    = Column(NVARCHAR(20),  nullable=True)
    term                        = Column(Integer,   nullable=True)
    ltv                         = Column(Numeric(10,2), nullable=True)
    mileage_max                 = Column(NVARCHAR(50),  nullable=True)
    stipulations                = Column(Text,      nullable=True)
    year_greaterthan            = Column(Integer,   nullable=True)
    member_source_id            = Column(NVARCHAR(100), nullable=True)
    preapproved_effective_date  = Column(DATETIMEOFFSET, nullable=True)
    logo_file_nm                = Column(NVARCHAR(255), nullable=True)
    lender_branch_fax           = Column(NVARCHAR(20),  nullable=True)
    logging_ind                 = Column(NVARCHAR(20),  nullable=True)
    person_type                 = Column(NVARCHAR(20),  nullable=True)
    notification_cd             = Column(Text,      nullable=True)
    msr_email_id                = Column(Text,      nullable=True)
    lender_website              = Column(NVARCHAR(255), nullable=True)
    branch_email                = Column(NVARCHAR(255), nullable=True)
    dealer_visits               = Column(Text,      nullable=True)
    after_hours_phn_num         = Column(NVARCHAR(20),  nullable=True)
    lender_id                   = Column(Integer,   nullable=True)
    loan_id                     = Column(NVARCHAR(255), nullable=True)
    fico_score                  = Column(Integer,   nullable=True)
    image                       = Column(Text,      nullable=True)
    wholesale_retail            = Column(NVARCHAR(20),  nullable=True)
    allowance                   = Column(Integer,   nullable=True)
    status                      = Column(Boolean,   nullable=False, default=True)
    deleted_at                  = Column(DATETIMEOFFSET, nullable=True)
    created_at                  = Column(DATETIMEOFFSET, nullable=False)
    updated_at                  = Column(DATETIMEOFFSET, nullable=False)
    latitude                    = Column(Numeric(11,6), nullable=True)
    longitude                   = Column(Numeric(11,6), nullable=True)
    zip_code                    = Column(NVARCHAR(10),  nullable=True)
    password                    = Column(NVARCHAR(256), nullable=True)
    logo_url                    = Column(NVARCHAR(1000), nullable=True)
    user_car_looking_for        = Column(NVARCHAR(30),  nullable=True)
    user_down_payment           = Column(Numeric(10,2), nullable=True)
    is_include_trade            = Column(Boolean,   nullable=True, default=False)
    user_worth                  = Column(Numeric(10,2), nullable=True)
    user_owe                    = Column(Numeric(10,2), nullable=True)
    user_total_purchase_power   = Column(Numeric(10,2), nullable=True)
    test_data                   = Column(Boolean,   nullable=True, default=False)
    member_type                 = Column(Integer,   nullable=True)
    user_vehicle_purchase_price = Column(Numeric(10,2), nullable=True)
    user_monthly_payment        = Column(Numeric(10,2), nullable=True)
    user_selected_monthly_term  = Column(Integer,   nullable=True)
    user_selected_interest_rate = Column(Numeric(10,2), nullable=True)
    tutorial_completion_status  = Column(Text,      nullable=True)
    is_loan_offer_accepted      = Column(Boolean,   nullable=False, default=False)
