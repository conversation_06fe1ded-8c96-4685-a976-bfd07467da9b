#!/usr/bin/env python3
"""
Development server runner for Vehicle Search AI Microservice
"""
import uvicorn
from app.config.settings import settings

if __name__ == "__main__":
    print("🚗 Starting Vehicle Search AI Microservice...")
    print("Server will be available at: http://localhost:8000")
    print("API Documentation: http://localhost:8000/docs")
    print("Alternative docs: http://localhost:8000/redoc")
    print(f"Environment: {settings.environment}")
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )

