import re
from typing import List

class VehicleQueryValidator:
    """Validator for vehicle-related queries"""
    
    # Vehicle-related keywords
    VEHICLE_KEYWORDS = [
        'car', 'cars', 'vehicle', 'vehicles', 'auto', 'automobile', 'automotive',
        'truck', 'trucks', 'suv', 'sedan', 'coupe', 'hatchback', 'convertible',
        'motorcycle', 'bike', 'scooter', 'van', 'minivan', 'pickup',
        'honda', 'toyota', 'ford', 'chevrolet', 'bmw', 'mercedes', 'audi',
        'volkswagen', 'nissan', 'hyundai', 'kia', 'mazda', 'subaru',
        'lexus', 'acura', 'infiniti', 'cadillac', 'buick', 'gmc',
        'jeep', 'ram', 'dodge', 'chrysler', 'lincoln', 'volvo',
        'porsche', 'ferrari', 'lamborghini', 'maserati', 'bentley',
        'engine', 'transmission', 'horsepower', 'mpg', 'fuel', 'gas',
        'electric', 'hybrid', 'diesel', 'mileage', 'price', 'cost',
        'buy', 'sell', 'purchase', 'lease', 'finance', 'loan',
        'new', 'used', 'certified', 'pre-owned', 'dealer', 'dealership',
        'model', 'year', 'make', 'trim', 'features', 'specs',
        'review', 'rating', 'comparison', 'vs', 'versus',
        'maintenance', 'repair', 'service', 'warranty', 'insurance'
    ]
    
    @classmethod
    def is_vehicle_related(cls, query: str) -> bool:
        """Check if the query is vehicle-related"""
        query_lower = query.lower().strip()

        # Handle greetings as vehicle-related (FastPass assistant should respond)
        greeting_patterns = [
            r'^(hi|hello|hey|good morning|good afternoon|good evening)[\s!.]*$',
            r'^(hi|hello|hey)\s+(there|fastpass)[\s!.]*$',
            r'^(what\'s up|how are you|howdy)[\s!.]*$'
        ]

        for pattern in greeting_patterns:
            if re.match(pattern, query_lower):
                return True

        # Handle vehicle search requests as vehicle-related
        if cls.is_vehicle_search_request(query_lower):
            return True

        # Check for vehicle keywords
        for keyword in cls.VEHICLE_KEYWORDS:
            if keyword in query_lower:
                return True

        # Check for model patterns (e.g., "A4", "F-150", "Model 3")
        model_patterns = [
            r'\b[A-Z]\d+\b',  # A4, X5, etc.
            r'\b[A-Z]-\d+\b',  # F-150, C-Class, etc.
            r'\bModel [A-Z0-9]+\b',  # Model 3, Model S, etc.
            r'\b\d{4}\b.*\b(model|year)\b',  # 2022 model, etc.
        ]

        for pattern in model_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                return True

        return False
    
    @classmethod
    def extract_search_intent(cls, query: str) -> str:
        """Extract the search intent from the query"""
        query_lower = query.lower().strip()

        # Handle greetings
        greeting_patterns = [
            r'^(hi|hello|hey|good morning|good afternoon|good evening)[\s!.]*$',
            r'^(hi|hello|hey)\s+(there|fastpass)[\s!.]*$',
            r'^(what\'s up|how are you|howdy)[\s!.]*$'
        ]

        for pattern in greeting_patterns:
            if re.match(pattern, query_lower):
                return 'greeting'

        # Check if user is asking to see vehicles (follow-up to information)
        if cls.is_vehicle_search_request(query_lower):
            return 'vehicle_search_request'

        search_keywords = ['show', 'find', 'search', 'look for', 'get me', 'list']
        filter_keywords = ['under', 'below', 'above', 'between', 'less than', 'more than']
        info_keywords = ['tell me about', 'what are', 'compare', 'review', 'features', 'specs', 'safety', 'benefits']

        # Check for information-seeking keywords first
        if any(keyword in query_lower for keyword in info_keywords):
            return 'information'

        # Check if query contains search keywords
        has_search_keyword = any(keyword in query_lower for keyword in search_keywords)

        # Check if query contains vehicle make/model (specific vehicle request)
        has_vehicle_specific = cls._has_specific_vehicle_mention(query_lower)

        # Check for filter criteria (price, year, etc.)
        has_filter_criteria = any(keyword in query_lower for keyword in filter_keywords) or cls._has_year_mention(query_lower)

        if has_search_keyword:
            # If it has search keywords and mentions specific vehicle or filter criteria, extract filters
            if has_vehicle_specific or has_filter_criteria:
                return 'search_with_filters'
            return 'search'

        # Even without explicit search keywords, if it mentions specific vehicle with criteria, treat as filter extraction
        if has_vehicle_specific and has_filter_criteria:
            return 'search_with_filters'

        return 'information'

    @classmethod
    def is_vehicle_search_request(cls, query: str) -> bool:
        """Check if user is asking to see specific vehicles"""
        query_lower = query.lower().strip()

        # Simple keywords that indicate user wants to see vehicles
        search_keywords = ['yes', 'yeah', 'yep', 'sure', 'okay', 'ok', 'show me', 'find', 'see them', 'see those', 'show them', 'find them']

        # Also check for short affirmative responses
        if query_lower in ['yes', 'yeah', 'yep', 'sure', 'okay', 'ok', 'y']:
            return True

        return any(keyword in query_lower for keyword in search_keywords)

    @classmethod
    def _has_specific_vehicle_mention(cls, query: str) -> bool:
        """Check if query mentions specific vehicle make/model"""
        # Common vehicle makes
        vehicle_makes = [
            'audi', 'bmw', 'mercedes', 'toyota', 'honda', 'ford', 'chevrolet', 'nissan',
            'volkswagen', 'hyundai', 'kia', 'mazda', 'subaru', 'lexus', 'acura', 'infiniti',
            'cadillac', 'lincoln', 'jeep', 'ram', 'dodge', 'chrysler', 'buick', 'gmc',
            'volvo', 'porsche', 'tesla', 'ferrari', 'lamborghini', 'bentley', 'maserati'
        ]

        # Check for vehicle makes
        for make in vehicle_makes:
            if make in query:
                return True

        # Check for model patterns (e.g., "A4", "X5", "Model 3")
        model_patterns = [
            r'\b[A-Z]\d+\b',  # A4, X5, etc.
            r'\b[A-Z]-\d+\b',  # F-150, C-Class, etc.
            r'\bModel [A-Z0-9]+\b',  # Model 3, Model S, etc.
        ]

        import re
        for pattern in model_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                return True

        return False

    @classmethod
    def _has_year_mention(cls, query: str) -> bool:
        """Check if query mentions a specific year"""
        import re
        year_pattern = r'\b(19|20)\d{2}\b'
        return bool(re.search(year_pattern, query))
