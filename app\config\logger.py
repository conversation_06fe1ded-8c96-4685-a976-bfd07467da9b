import logging
import uuid
import async<PERSON>
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from fastapi import Request
import os
import socket
import json
from datetime import datetime
from azure.storage.blob import BlobServiceClient
from azure.core.exceptions import ResourceExistsError  # ✅ Added
import subprocess
from logging.handlers import TimedRotatingFileHandler
from app.config.settings import settings
# Load environment variables
AZURE_BLOB_ACCOUNT = settings.AZURE_BLOB_ACCOUNT
AZURE_BLOB_KEY = settings.AZURE_BLOB_KEY
LOG_ENVIRONMENT =settings.LOG_ENVIRONMENT
CONTAINER_NAME = settings.BLOB_CONTAINER_NAME
EC2 = os.getenv('EC2', socket.gethostname())

# Azure Blob Setup - Only if credentials are available
blob_service_client = None
container_client = None

if AZURE_BLOB_ACCOUNT and AZURE_BLOB_KEY:
    try:
        blob_service_client = BlobServiceClient(
            account_url=f"https://{AZURE_BLOB_ACCOUNT}.blob.core.windows.net/",
            credential=AZURE_BLOB_KEY
        )
        container_client = blob_service_client.get_container_client(CONTAINER_NAME)

        # ✅ Fixed container creation logic
        try:
            container_client.create_container()
        except ResourceExistsError:
            pass
        print("✅ Azure Blob Storage logging enabled")
    except Exception as e:
        print(f"⚠️ Azure Blob Storage setup failed: {e}")
        blob_service_client = None
        container_client = None
else:
    print("⚠️ Azure Blob Storage credentials not found - logging to Azure disabled")

def format_ip(ip):
    if not ip:
        return "Unknown IP"
    if ip.startswith("::ffff:"):
        return ip.split(":")[-1]
    return ip

def get_current_commit_hash():
    try:
        commit_hash = subprocess.check_output(
            ['git', 'log', '--format=%H', '-n', '1', 'HEAD']
        ).decode('utf-8').strip()
        return commit_hash[:7]
    except Exception as e:
        print(f"Error retrieving commit hash: {e}")
        return "unknown"

# Custom formatter
class CustomJsonFormatter(logging.Formatter):
    def format(self, record):
        metadata = getattr(record, "metadata", {})
        log_entry = {
            "@timestamp": datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S.%fZ'),
            "V": metadata.get("V", ""),
            "EC2": metadata.get("EC2", ""),
            "IP": metadata.get("IP", ""),
            "TID": metadata.get("TID", ""),
            "MEMBER_ID": metadata.get("MEMBER_ID", ""),
            "PU": metadata.get("PU", ""),
            "USER_AGENT": metadata.get("USER_AGENT", ""),
            "LEVEL": record.levelname.lower(),
            "MESSAGE": record.getMessage()
        }
        return json.dumps(log_entry)

# File rotation to blob name per day
def get_message_blob_name():
    date_str = datetime.utcnow().strftime('%Y-%m-%d')
    return f"{LOG_ENVIRONMENT}-fastpass-ai-microservice-{date_str}.log"

# Logger instance
logger = logging.getLogger("azureMessageLogger")
logger.setLevel(logging.DEBUG)
logger.propagate = False
logging.getLogger("azure.core.pipeline.policies.http_logging_policy").setLevel(logging.WARNING)

class AzureBlobMessageHandler(logging.Handler):
    def __init__(self):
        super().__init__()
        self.executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="azure_blob_logger")

    def emit(self, record):
        # Only log to Azure if container_client is available
        if container_client is None:
            return

        # Submit to thread pool for async execution - don't wait for completion
        try:
            msg = self.format(record)
            self.executor.submit(self._async_emit_to_blob, msg)

        except Exception as e:
            print(f"Failed to submit log to async thread: {e}")

    def _async_emit_to_blob(self, msg):
        """Execute the actual blob write in a separate thread"""
        try:
            blob_name = get_message_blob_name()
            blob_client = container_client.get_blob_client(blob_name)

            # ✅ Ensure it's created as an Append Blob
            if not blob_client.exists():
                blob_client.create_append_blob()

            # ✅ Append log entry
            blob_client.append_block(msg + "\n")

        except Exception as e:
            print(f"Failed to write to Azure Blob (async): {e}")


class LocalFileMessageHandler(logging.Handler):
    """Local file handler that mirrors Azure Blob functionality with async pattern"""
    def __init__(self):
        super().__init__()
        self.executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="local_file_logger")
        # Create logs directory if it doesn't exist
        os.makedirs("logs", exist_ok=True)

    def emit(self, record):
        # Submit to thread pool for async execution - don't wait for completion
        try:
            msg = self.format(record)
            self.executor.submit(self._async_emit_to_file, msg)
        except Exception as e:
            print(f"Failed to submit log to local file async thread: {e}")

    def _async_emit_to_file(self, msg):
        """Execute the actual file write in a separate thread"""
        try:
            log_filename = self._get_local_log_filename()
            log_path = os.path.join("logs", log_filename)

            with open(log_path, "a", encoding="utf-8") as f:
                f.write(msg + "\n")
        except Exception as e:
            print(f"Failed to write to local log file (async): {e}")

    def _get_local_log_filename(self):
        """Generate local log filename similar to Azure blob naming"""
        date_str = datetime.utcnow().strftime('%Y-%m-%d')
        return f"fastpass-ai-microservice-{date_str}.log"


# Add local file handler if LOCAL_LOGGING_ENABLED is true
if settings.LOCAL_LOGGING_ENABLED:
    local_handler = LocalFileMessageHandler()
    local_handler.setFormatter(CustomJsonFormatter())
    logger.addHandler(local_handler)
    print("✅ Local file logging enabled for main logger")
elif not settings.LOCAL_LOGGING_ENABLED:
    # Only add Azure handler if Azure Blob Storage is configured
    azure_handler = AzureBlobMessageHandler()
    azure_handler.setFormatter(CustomJsonFormatter())
    logger.addHandler(azure_handler)

# Logging Helper
def log_info(req: Request, context: str):
    try:
        headers = req.headers
        client_ip = headers.get("x-forwarded-for") or req.client.host
        transaction_id = req.state.transaction
        # Safe extraction from dictionary
        user = getattr(req.state, "user", {})
        member_id = user.get("member_id", "")  # Assuming user is a dict
        metadata = {
            "V": get_current_commit_hash(),
            "EC2": EC2,
            "IP": client_ip,
            "TID": transaction_id,
            "MEMBER_ID": member_id,
            "PU": req.url.hostname or "",
            "USER_AGENT": headers.get("user-agent", "")
        }

        logger.info(context, extra={"metadata": metadata})

    except Exception as e:
        print(f"Logger info error: {e}")

def log_error(req: Request, context: str, metadata=None):
    try:
        headers = req.headers
        client_ip = headers.get("x-forwarded-for") or req.client.host
        transaction_id = headers.get("transaction-id", str(uuid.uuid4()))
        # Safe extraction from dictionary
        user = getattr(req.state, "user", {})
        member_id = user.get("member_id", "")  # Assuming user is a dict
        base_metadata = {
            "V": get_current_commit_hash(),
            "EC2": EC2,
            "IP": client_ip,
            "TID": transaction_id,
            "MEMBER_ID": member_id,
            "PU": req.url.hostname or "",
            "USER_AGENT": headers.get("user-agent", "")
        }
        if metadata:
            base_metadata.update(metadata)

        logger.error(context, extra={"metadata": base_metadata})

    except Exception as e:
        print(f"Logger error: {e}")
