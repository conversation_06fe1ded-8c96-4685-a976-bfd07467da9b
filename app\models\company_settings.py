from datetime import datetime, timezone
from typing import List, Optional
from pydantic import BaseModel, Field
from bson import ObjectId

class PyObjectId(ObjectId):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v, _):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)

    @classmethod
    def __get_pydantic_json_schema__(cls, field_schema):
        field_schema.update(type="string")

class SystemPromptVersion(BaseModel):
    """Model for system prompt versions"""
    version: int = Field(..., description="Version number")
    prompt: str = Field(..., description="System prompt content")
    created_by: str = Field(..., description="User who created this version")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    is_active: bool = Field(default=False, description="Whether this version is currently active")
    description: Optional[str] = Field(None, description="Description of changes in this version")

class CompanySettings(BaseModel):
    """Model for company settings including system prompts"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    company_id: str = Field(..., description="Company identifier")
    setting_type: str = Field(..., description="Type of setting (e.g., 'system_prompt')")
    setting_name: str = Field(..., description="Name of the setting")
    current_version: int = Field(default=1, description="Current active version")
    versions: List[SystemPromptVersion] = Field(default=[], description="List of all versions")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    class Config:
        allow_population_by_field_name = True
        json_encoders = {ObjectId: str}

class SystemPromptRequest(BaseModel):
    """Request model for updating system prompt"""
    prompt: str = Field(..., description="New system prompt content")
    description: Optional[str] = Field(None, description="Description of changes")
    created_by: str = Field(..., description="User making the change")

class SystemPromptResponse(BaseModel):
    """Response model for system prompt operations"""
    success: bool = Field(..., description="Operation success status")
    message: str = Field(..., description="Response message")
    current_prompt: Optional[str] = Field(None, description="Current active prompt")
    current_version: Optional[int] = Field(None, description="Current version number")
    versions: Optional[List[SystemPromptVersion]] = Field(None, description="List of versions")

class RestoreVersionRequest(BaseModel):
    """Request model for restoring a system prompt version"""
    version: int = Field(..., description="Version number to restore")
    created_by: str = Field(..., description="User performing the restore")
