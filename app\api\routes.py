from fastapi import APIRouter
# Import all route modules
from app.api.vehicle_routes import router as vehicle_router
from app.api.system_routes import router as system_router
from app.api.test_routes import router as test_router
from app.api.health_routes import router as health_router

# Create main router
router = APIRouter()

# Include all route modules
router.include_router(vehicle_router, tags=["Vehicle"])
router.include_router(system_router, tags=["System"])
router.include_router(test_router, tags=["Testing"])
router.include_router(health_router, tags=["Health"])

