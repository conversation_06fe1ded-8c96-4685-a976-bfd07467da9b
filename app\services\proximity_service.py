import os
import math
import csv
from typing import List
from fastapi import Request
from app.config.settings import settings
from app.config.logger import log_info, log_error
class ProximityService:
    def __init__(self, req:Request):
        # Set req first to ensure it's available for error logging
        self.req = req
        try:
            # Use getattr with default value to handle missing DISTANCE setting
            self.max_user_distance = getattr(settings, 'DISTANCE', 25) or 25
            self.dealers_path = settings.DEALER_FILE_PATH
        except Exception as e:
            log_error(self.req, f"Error initializing ProximityService: {str(e)}")
            raise

    def to_radians(self, degrees: float) -> float:
        try:
            result = math.radians(degrees)
            return result
        except Exception as e:
            log_error(self.req, f"Error in to_radians({degrees}): {str(e)}")
            raise

    def haversine_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        try:
            R = 3958.8  # Earth radius in miles

            d_lat = self.to_radians(lat2 - lat1)
            d_lon = self.to_radians(lon2 - lon1)

            a = (math.sin(d_lat / 2) ** 2 +
                 math.cos(self.to_radians(lat1)) *
                 math.cos(self.to_radians(lat2)) *
                 math.sin(d_lon / 2) ** 2)

            c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
            distance = R * c

            return distance
        except Exception as e:
            log_error(self.req, f"Error in haversine_distance: {str(e)}")
            raise

    async def read_dealers_from_file(self, filename: str) -> List[dict]:
        dealers = []
        try:
            with open(filename, 'r', encoding='utf-8') as file:
                reader = csv.reader(file, delimiter='|')
                line_count = 0
                valid_dealers = 0

                for line_num, row in enumerate(reader, start=1):
                    line_count += 1
                    if len(row) < 5:
                        log_error(self.req, f"Skipping line {line_num}: Expected at least 5 fields, got {len(row)}")
                        continue
                    try:
                        dealer_id, name, lat, lon, dealer_type = row[:5]
                        if dealer_type.strip().lower().replace('"', '') == 'franchise':
                            dealers.append({
                                "dealer_id": dealer_id,
                                "name": name,
                                "lat": float(lat),
                                "lon": float(lon)
                            })
                            valid_dealers += 1
                    except Exception as inner_err:
                        log_error(self.req, f"Error parsing line {line_num}: {inner_err}")

            log_info(self.req, f"Successfully read {len(dealers)} franchise dealers from file: {filename}")
            return dealers
        except Exception as e:
            log_error(self.req, f"Error reading dealers from file {filename}: {str(e)}")
            raise

    async def find_nearby_dealers(self, person_location: dict, dealers: List[dict], max_distance: float) -> List[dict]:
        try:
            nearby = []

            for dealer in dealers:
                distance = self.haversine_distance(
                    person_location["lat"],
                    person_location["lon"],
                    dealer["lat"],
                    dealer["lon"]
                )
                if float(distance) <= float(max_distance):
                    nearby.append({
                        "dealer_id": dealer["dealer_id"],
                        "name": dealer["name"],
                        "distance": round(distance, 2)
                    })

            log_info(self.req, f"Found {len(nearby)} nearby dealers within {max_distance} miles")
            return nearby
        except Exception as e:
            log_error(self.req, f"Error finding nearby dealers: {str(e)}")
            raise

    async def fetch_dealers(self, req: Request) -> List[dict]:
        try:
            body = await req.json()

            latitude = float(body.get("lat"))
            longitude = float(body.get("long"))
            distance = float(body.get("distance", self.max_user_distance))  # fallback to 25 if unset

            log_info(req, f"Received location: lat={latitude}, lon={longitude}, distance={distance}")

            person_location = {"lat": latitude, "lon": longitude}
            dealer_file = self.dealers_path

            dealers = await self.read_dealers_from_file(dealer_file)
            nearby_dealers = await self.find_nearby_dealers(person_location, dealers, distance)

            return nearby_dealers
        except Exception as e:
            log_error(req, f"Error fetching dealers: {str(e)}")
            raise
