# app/crud/user.py

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.db.session import DatabaseSession
from app.models.users import User  # make sure this path is correct
from app.config.logger import log_info, log_error
from fastapi import Request

class userService:
    def __init__(self, req: Request):
        try:
            self.req = req
            log_info(self.req, "userService initialized successfully.")
        except Exception as e:
            log_error(self.req, f"Error initializing userService: {str(e)}")
            raise

    async def connect(self):
        """Establish async database connection"""
        try:
            self.session = await DatabaseSession.get_async_session()
        except Exception as e:
            log_error(self.req, f"Error connecting to database: {str(e)}")
            raise

    async def close(self):
        """Close async database connection"""
        try:
            if hasattr(self, 'session') and self.session:
                await self.session.close()
        except Exception as e:
            log_error(self.req, f"Error closing database connection: {str(e)}")
            raise

    async def get_user_by_id(self, user_id: int) -> list[User]:
        try:
            log_info(self.req, f"Fetching user(s) with user_id: {user_id}")

            # Build async query using select
            stmt = select(User).where(User.user_id == user_id)
            result = await self.session.execute(stmt)
            users = result.scalars().all()

            log_info(self.req, f"Found {len(users)} user(s) with user_id: {user_id}")
            return list(users)
        except Exception as e:
            log_error(self.req, f"Error in get_user_by_id({user_id}): {str(e)}")
            raise



    async def get_user_loggedin_user_details(self, member_id: int) -> dict:
        try:
            log_info(self.req, f"Fetching logged-in user details for member_id: {member_id}")

            selected_fields = [
                User.user_id,
                User.preapproved_max_amt,
                User.user_car_looking_for,
                User.user_down_payment,
                User.is_include_trade,
                User.user_worth,
                User.user_owe,
                User.user_total_purchase_power,
                User.latitude,
                User.longitude,
                User.zip_code,
                User.logo_url,
                User.loan_id,
                User.member_id,
                User.member_first_name,
                User.member_last_name,
                User.member_type,
                User.tutorial_completion_status,
                User.is_loan_offer_accepted,
            ]

            # Build async query using select
            stmt = select(*selected_fields).where(User.member_id == member_id)
            result = await self.session.execute(stmt)
            row = result.first()

            if not row:
                log_info(self.req, f"No user found for member_id: {member_id}")
                return {}

            user_dict = { col.key: getattr(row, col.key) for col in selected_fields }

            log_info(self.req, f"User details fetched for member_id: {member_id}")
            return user_dict
        except Exception as e:
            log_error(self.req, f"Error in get_user_loggedin_user_details({member_id}): {str(e)}")
            raise
