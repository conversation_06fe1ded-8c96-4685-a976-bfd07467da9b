import re
from typing import Dict, Any, List

class HTMLFormatter:
    """Utility class for formatting responses as HTML"""
    
    @staticmethod
    def format_vehicle_info(content: str, title: str = None) -> str:
        """Format vehicle information as HTML"""
        html = "<div class='vehicle-info'>"
        
        if title:
            html += f"<h3 class='vehicle-title'>{title}</h3>"
        
        # Convert markdown-like formatting to HTML
        content = HTMLFormatter._convert_markdown_to_html(content)
        
        html += f"<div class='vehicle-content'>{content}</div>"
        html += "</div>"
        
        return html
    
    @staticmethod
    def format_search_results(results: List[Dict[str, Any]]) -> str:
        """Format search results as HTML"""
        html = "<div class='search-results'>"
        html += "<h3>Search Results</h3>"
        
        for i, result in enumerate(results, 1):
            html += f"<div class='result-item'>"
            html += f"<h4>{i}. {result.get('title', 'No title')}</h4>"
            html += f"<p>{result.get('snippet', 'No description available')}</p>"
            if result.get('link'):
                html += f"<a href='{result['link']}' target='_blank'>Read more</a>"
            html += "</div>"
        
        html += "</div>"
        return html
    
    @staticmethod
    def format_error_message(error: str) -> str:
        """Format error message as HTML"""
        return f"<div class='error-message'><p class='error'>⚠️ {error}</p></div>"
    
    @staticmethod
    def format_vehicle_not_related() -> str:
        """Format non-vehicle related response"""
        return """
        <div class='not-vehicle-related'>
            <h3>🚗 FastPass Vehicle Expert</h3>
            <p>I'm your FastPass vehicle expert, and I can only help with vehicle-related questions. Please ask me about:</p>
            <ul>
                <li>Vehicle specifications and features</li>
                <li>Car comparisons and reviews</li>
                <li>Vehicle search and recommendations on FastPass</li>
                <li>Automotive market information</li>
                <li>Vehicle maintenance and technical details</li>
                <li>Finding specific vehicles available on FastPass</li>
            </ul>
            <p>How can I help you find the perfect vehicle on FastPass today?</p>
        </div>
        """
    
    @staticmethod
    def _convert_markdown_to_html(text: str) -> str:
        """Convert markdown formatting to HTML"""
        # If already contains HTML tags, minimal processing
        if '<' in text and '>' in text:
            # Just clean up any remaining markdown
            text = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', text)
            text = re.sub(r'__(.*?)__', r'<strong>\1</strong>', text)
            text = re.sub(r'\*(.*?)\*', r'<em>\1</em>', text)
            text = re.sub(r'_(.*?)_', r'<em>\1</em>', text)
            return text

        # Convert markdown headings (do this before table processing)
        text = re.sub(r'^\s*### (.*?)$', r'<h4>\1</h4>', text, flags=re.MULTILINE)
        text = re.sub(r'^\s*## (.*?)$', r'<h3>\1</h3>', text, flags=re.MULTILINE)
        text = re.sub(r'^\s*# (.*?)$', r'<h3>\1</h3>', text, flags=re.MULTILINE)

        # Also handle bold text that looks like headings
        text = re.sub(r'^\s*\*\*(.*?):\*\*\s*$', r'<h4>\1</h4>', text, flags=re.MULTILINE)

        # Convert markdown tables
        lines = text.split('\n')
        in_table = False
        formatted_lines = []
        table_header_found = False

        for i, line in enumerate(lines):
            original_line = line
            line = line.strip()

            # Check if this is a table row (has | and at least 2 cells)
            if '|' in line and line.count('|') >= 2:
                # Skip separator lines like |---|---|
                if re.match(r'^\s*\|[\s\-\|]+\|\s*$', line):
                    continue

                if not in_table:
                    formatted_lines.append('<table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">')
                    in_table = True
                    table_header_found = False

                # Split by | and create table row
                cells = [cell.strip() for cell in line.split('|')]
                # Remove empty cells from start and end
                while cells and not cells[0]:
                    cells.pop(0)
                while cells and not cells[-1]:
                    cells.pop()

                if cells:  # Only process if we have cells
                    # First row with content is header
                    if not table_header_found:
                        row_html = '<tr>' + ''.join(f'<th style="padding: 8px; background-color: #f5f5f5; font-weight: bold;">{cell}</th>' for cell in cells) + '</tr>'
                        table_header_found = True
                    else:
                        row_html = '<tr>' + ''.join(f'<td style="padding: 8px;">{cell}</td>' for cell in cells) + '</tr>'

                    formatted_lines.append(row_html)
            else:
                if in_table:
                    formatted_lines.append('</table>')
                    in_table = False
                    table_header_found = False
                formatted_lines.append(original_line)

        if in_table:
            formatted_lines.append('</table>')

        text = '\n'.join(formatted_lines)

        # Convert bullet points
        text = re.sub(r'^[\s]*[-\*\+]\s+(.*?)$', r'<li>\1</li>', text, flags=re.MULTILINE)

        # Wrap consecutive <li> items in <ul>
        text = re.sub(r'(<li>.*?</li>)(\s*<li>.*?</li>)*', lambda m: '<ul>' + m.group(0) + '</ul>', text, flags=re.DOTALL)

        # Bold text
        text = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', text)
        text = re.sub(r'__(.*?)__', r'<strong>\1</strong>', text)

        # Italic text
        text = re.sub(r'\*(.*?)\*', r'<em>\1</em>', text)
        text = re.sub(r'_(.*?)_', r'<em>\1</em>', text)

        # Convert links
        text = re.sub(r'\[([^\]]+)\]\(([^)]+)\)', r'<a href="\2" target="_blank">\1</a>', text)

        # Handle paragraphs
        paragraphs = text.split('\n\n')
        formatted_paragraphs = []

        for para in paragraphs:
            para = para.strip()
            if para:
                # Don't wrap if already has HTML tags
                if not any(tag in para for tag in ['<h', '<ul>', '<table>', '<li>']):
                    # Replace single line breaks with <br>
                    para = para.replace('\n', '<br>')
                    if not para.startswith('<'):
                        para = f'<p>{para}</p>'
                formatted_paragraphs.append(para)
            else:
                formatted_paragraphs.append(para)

        return '\n'.join(formatted_paragraphs)
